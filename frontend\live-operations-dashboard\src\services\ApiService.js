import axios from 'axios';

class ApiService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8100';
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        console.error('API Error:', error);
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('authToken');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Dashboard APIs
  async getDashboardData() {
    return this.api.get('/api/live-ops/dashboard');
  }

  async getFleetStatus() {
    return this.api.get('/api/live-ops/fleet/status');
  }

  async getLiveRides() {
    return this.api.get('/api/live-ops/rides/live');
  }

  async getOperationalStatistics() {
    return this.api.get('/api/live-ops/statistics');
  }

  // Alert APIs
  async sendEmergencyAlert(alertData) {
    return this.api.post('/api/live-ops/alerts/emergency', alertData);
  }

  // Driver APIs
  async updateDriverStatus(driverId, statusData) {
    return this.api.put(`/api/live-ops/drivers/${driverId}/status`, statusData);
  }

  // Fleet Management APIs
  async getFleetLocations() {
    return this.api.get('/api/live-ops/fleet/locations');
  }

  async getVehicleDetails(vehicleId) {
    return this.api.get(`/api/live-ops/fleet/vehicles/${vehicleId}`);
  }

  // Ride Management APIs
  async getRideDetails(rideId) {
    return this.api.get(`/api/live-ops/rides/${rideId}`);
  }

  async assignRide(rideId, driverId) {
    return this.api.post(`/api/live-ops/rides/${rideId}/assign`, { driverId });
  }

  async cancelRide(rideId, reason) {
    return this.api.post(`/api/live-ops/rides/${rideId}/cancel`, { reason });
  }

  // Analytics APIs
  async getRealtimeMetrics() {
    return this.api.get('/api/live-ops/analytics/realtime');
  }

  async getPerformanceMetrics(timeRange = '24h') {
    return this.api.get(`/api/live-ops/analytics/performance?range=${timeRange}`);
  }

  // Configuration APIs
  async getSystemConfig() {
    return this.api.get('/api/live-ops/config');
  }

  async updateSystemConfig(config) {
    return this.api.put('/api/live-ops/config', config);
  }

  // Health Check
  async healthCheck() {
    return this.api.get('/actuator/health');
  }
}

// Export singleton instance
export default new ApiService();
