@echo off
echo ========================================
echo    TECNO DRIVE HEALTH CHECK
echo ========================================
echo.

echo Infrastructure Services:
echo [1] Eureka Server (8761):
curl -s -w "Status: %%{http_code}\n" http://localhost:8761/actuator/health -o nul

echo [2] Config Server (8888):
curl -s -w "Status: %%{http_code}\n" http://localhost:8888/actuator/health -o nul

echo [3] API Gateway (8080):
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/actuator/health -o nul

echo [4] Monitoring Service (9090):
curl -s -w "Status: %%{http_code}\n" http://localhost:9090/actuator/health -o nul

echo.
echo Core Services:
echo [5] Auth Service (8081):
curl -s -w "Status: %%{http_code}\n" http://localhost:8081/actuator/health -o nul

echo [6] User Service (8082):
curl -s -w "Status: %%{http_code}\n" http://localhost:8082/actuator/health -o nul

echo [7] Payment Service (8083):
curl -s -w "Status: %%{http_code}\n" http://localhost:8083/actuator/health -o nul

echo.
echo Business Services:
echo [8] Ride Service (8084):
curl -s -w "Status: %%{http_code}\n" http://localhost:8084/actuator/health -o nul

echo [9] Fleet Service (8085):
curl -s -w "Status: %%{http_code}\n" http://localhost:8085/actuator/health -o nul

echo [10] Location Service (8086):
curl -s -w "Status: %%{http_code}\n" http://localhost:8086/actuator/health -o nul

echo [11] Analytics Service (8087):
curl -s -w "Status: %%{http_code}\n" http://localhost:8087/actuator/health -o nul

echo.
echo ========================================
echo    HEALTH CHECK COMPLETE
echo ========================================
echo.
echo Status Codes:
echo - 200: Service is healthy
echo - 404: Service not found
echo - Connection refused: Service not running
echo.
