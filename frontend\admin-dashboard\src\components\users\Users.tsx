import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>ton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
} from '@mui/material';
import {
  People,
  PersonAdd,
  DriveEta,
  AdminPanelSettings,
  Block,
  CheckCircle,
  Edit,
  Delete,
} from '@mui/icons-material';

const Users: React.FC = () => {
  const recentUsers = [
    {
      id: 'USR-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'driver',
      status: 'active',
      joinDate: '2024-01-15',
      avatar: '',
    },
    {
      id: 'USR-002',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'passenger',
      status: 'active',
      joinDate: '2024-01-20',
      avatar: '',
    },
    {
      id: 'USR-003',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      joinDate: '2024-01-10',
      avatar: '',
    },
    {
      id: 'USR-004',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'driver',
      status: 'suspended',
      joinDate: '2024-01-05',
      avatar: '',
    },
  ];

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'driver':
        return 'primary';
      case 'passenger':
        return 'success';
      default:
        return 'default';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <AdminPanelSettings />;
      case 'driver':
        return <DriveEta />;
      case 'passenger':
        return <People />;
      default:
        return <People />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'suspended':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle />;
      case 'suspended':
        return <Block />;
      default:
        return <CheckCircle />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        User Management
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <People color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Users</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                1,248
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Registered users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DriveEta color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Drivers</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                342
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active drivers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <People color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Passengers</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                896
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Regular passengers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AdminPanelSettings color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Admins</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                10
              </Typography>
              <Typography variant="body2" color="text.secondary">
                System administrators
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Recent Users
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button variant="outlined" startIcon={<Edit />}>
                    Bulk Edit
                  </Button>
                  <Button variant="contained" startIcon={<PersonAdd />}>
                    Add User
                  </Button>
                </Box>
              </Box>
              
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>User</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Join Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                              {user.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {user.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {user.id}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip
                            icon={getRoleIcon(user.role)}
                            label={user.role}
                            color={getRoleColor(user.role) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            icon={getStatusIcon(user.status)}
                            label={user.status}
                            color={getStatusColor(user.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{user.joinDate}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button size="small" startIcon={<Edit />}>
                              Edit
                            </Button>
                            <Button size="small" color="error" startIcon={<Delete />}>
                              Delete
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Users;
