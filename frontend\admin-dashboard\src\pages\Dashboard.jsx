import React, { useEffect, useState, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  useTheme,
  alpha,
  Paper,
  Badge,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  DirectionsCar,
  People,
  AttachMoney,
  TrendingUp,
  TrendingDown,
  Refresh,
  MoreVert,
  Warning,
  CheckCircle,
  Error,
  Info,
  Speed,
  LocalShipping,
  Schedule,
  LocationOn,
  Navigation,
  Timeline,
  Assessment,
  Notifications,
  Settings,
  ExpandMore,
  Map,
  Traffic,
  LocalGasStation,
  Battery80,
  SignalWifi4Bar,
  Gps,
  Route,
  Timer,
  Star,
  Phone,
  Message,
  Emergency,
  Security,
  Analytics,
  Dashboard as DashboardIcon,
  RealTimeUpdate,
  MonitorHeart,
  NetworkCheck,
  CloudSync,
  DataUsage,
  Memory,
  Storage,
  Computer,
} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { useDashboardStore } from '../store/dashboardStore';
import { WebSocketService } from '../services/WebSocketService';
import { motion } from 'framer-motion';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

const Dashboard = () => {
  const theme = useTheme();
  const {
    realTimeData,
    analytics,
    isLoading,
    error,
    lastUpdated,
    initializeDashboard,
    refreshDashboard,
    autoRefresh,
    toggleAutoRefresh,
  } = useDashboardStore();

  const [connectionStatus, setConnectionStatus] = useState({ connected: false });

  useEffect(() => {
    initializeDashboard();
  }, [initializeDashboard]);

  useEffect(() => {
    const updateConnectionStatus = () => {
      try {
        if (WebSocketService && typeof WebSocketService.isConnected === 'function') {
          setConnectionStatus({ connected: WebSocketService.isConnected() });
        } else {
          setConnectionStatus({ connected: false });
        }
      } catch (error) {
        console.warn('Error checking WebSocket connection:', error);
        setConnectionStatus({ connected: false });
      }
    };

    const handleRealTimeUpdate = (data) => {
      // Handle real-time updates from WebSocket
      console.log('Real-time update:', data);
    };

    // Set initial connection status
    updateConnectionStatus();

    // For now, we'll simulate connection status updates
    const interval = setInterval(updateConnectionStatus, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const kpiCards = [
    {
      title: 'Active Vehicles',
      value: realTimeData.activeVehicles || 0,
      icon: DirectionsCar,
      color: theme.palette.primary.main,
      trend: '+12%',
      trendUp: true,
    },
    {
      title: 'Active Drivers',
      value: realTimeData.activeDrivers || 0,
      icon: People,
      color: theme.palette.success.main,
      trend: '+8%',
      trendUp: true,
    },
    {
      title: 'Total Revenue',
      value: `$${(realTimeData.revenue || 0).toLocaleString()}`,
      icon: AttachMoney,
      color: theme.palette.warning.main,
      trend: '+15%',
      trendUp: true,
    },
    {
      title: 'Completed Trips',
      value: realTimeData.totalTrips || 0,
      icon: LocalShipping,
      color: theme.palette.info.main,
      trend: '+5%',
      trendUp: true,
    },
  ];

  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'Vehicle Maintenance Due',
      message: 'Vehicle #VH-001 requires scheduled maintenance',
      time: '5 minutes ago',
    },
    {
      id: 2,
      type: 'error',
      title: 'Driver Offline',
      message: 'Driver John Doe has been offline for 2 hours',
      time: '15 minutes ago',
    },
    {
      id: 3,
      type: 'info',
      title: 'New Trip Request',
      message: 'High-priority delivery request received',
      time: '30 minutes ago',
    },
  ];

  const getAlertIcon = (type) => {
    switch (type) {
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Error color="error" />;
      case 'success': return <CheckCircle color="success" />;
      default: return <Info color="info" />;
    }
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: alpha(theme.palette.divider, 0.1),
        },
      },
      x: {
        grid: {
          color: alpha(theme.palette.divider, 0.1),
        },
      },
    },
  };

  const tripsChartData = {
    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
    datasets: [
      {
        label: 'Trips',
        data: [12, 19, 25, 35, 28, 22],
        borderColor: theme.palette.primary.main,
        backgroundColor: alpha(theme.palette.primary.main, 0.1),
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const revenueChartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
        backgroundColor: theme.palette.success.main,
        borderColor: theme.palette.success.dark,
        borderWidth: 1,
      },
    ],
  };

  const vehicleStatusData = {
    labels: ['Active', 'Maintenance', 'Offline'],
    datasets: [
      {
        data: [65, 15, 20],
        backgroundColor: [
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.main,
        ],
        borderWidth: 0,
      },
    ],
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <Box sx={{ flexGrow: 1 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Last updated: {lastUpdated ? new Date(lastUpdated).toLocaleTimeString() : 'Never'}
              </Typography>
              <Chip
                label={connectionStatus.connected ? 'LIVE' : 'OFFLINE'}
                color={connectionStatus.connected ? 'success' : 'error'}
                size="small"
                icon={connectionStatus.connected ? <CheckCircle /> : <Error />}
              />
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={refreshDashboard}
              disabled={isLoading}
            >
              Refresh
            </Button>
            <Button
              variant={autoRefresh ? 'contained' : 'outlined'}
              startIcon={<Schedule />}
              onClick={toggleAutoRefresh}
            >
              Auto Refresh
            </Button>
          </Box>
        </Box>

        {isLoading && <LinearProgress sx={{ mb: 2 }} />}

        {/* KPI Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {kpiCards.map((kpi, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <motion.div variants={itemVariants}>
                <Card
                  sx={{
                    height: '100%',
                    background: `linear-gradient(135deg, ${alpha(kpi.color, 0.1)} 0%, ${alpha(kpi.color, 0.05)} 100%)`,
                    border: `1px solid ${alpha(kpi.color, 0.2)}`,
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {kpi.title}
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" sx={{ color: kpi.color }}>
                          {kpi.value}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          {kpi.trendUp ? (
                            <TrendingUp sx={{ color: theme.palette.success.main, fontSize: 16 }} />
                          ) : (
                            <TrendingDown sx={{ color: theme.palette.error.main, fontSize: 16 }} />
                          )}
                          <Typography
                            variant="body2"
                            sx={{
                              color: kpi.trendUp ? theme.palette.success.main : theme.palette.error.main,
                              ml: 0.5,
                            }}
                          >
                            {kpi.trend}
                          </Typography>
                        </Box>
                      </Box>
                      <Avatar sx={{ bgcolor: kpi.color, width: 48, height: 48 }}>
                        <kpi.icon />
                      </Avatar>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <motion.div variants={itemVariants}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="600">
                      Trips Overview
                    </Typography>
                    <IconButton size="small">
                      <MoreVert />
                    </IconButton>
                  </Box>
                  <Box sx={{ height: 300 }}>
                    <Line data={tripsChartData} options={chartOptions} />
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Vehicle Status
                  </Typography>
                  <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Doughnut
                      data={vehicleStatusData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'bottom',
                          },
                        },
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* Revenue Chart and Alerts */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <motion.div variants={itemVariants}>
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Weekly Revenue
                  </Typography>
                  <Box sx={{ height: 300 }}>
                    <Bar data={revenueChartData} options={chartOptions} />
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Recent Alerts
                  </Typography>
                  <List>
                    {alerts.map((alert, index) => (
                      <React.Fragment key={alert.id}>
                        <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                          <ListItemAvatar>
                            {getAlertIcon(alert.type)}
                          </ListItemAvatar>
                          <ListItemText
                            primary={alert.title}
                            secondary={
                              <>
                                <Typography variant="body2" color="text.secondary">
                                  {alert.message}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {alert.time}
                                </Typography>
                              </>
                            }
                          />
                        </ListItem>
                        {index < alerts.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>
      </Box>
    </motion.div>
  );
};

export default Dashboard;
