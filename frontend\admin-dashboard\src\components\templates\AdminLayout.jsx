import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Too<PERSON>bar,
  List,
  Typography,
  Divider,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Tooltip,
  useTheme,
  useMediaQuery,
  Chip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft,
  ChevronRight,
  Dashboard,
  DirectionsCar,
  People,
  Analytics,
  Settings,
  Notifications,
  AccountCircle,
  Logout,
  DarkMode,
  LightMode,
  ExpandLess,
  ExpandMore,
  LiveTv,
  Payment,
  LocalShipping,
  Security,
  Help,
  Feedback,
  Language,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { useThemeStore } from '../../store/themeStore';
import { WebSocketService } from '../../services/WebSocketService';

const drawerWidth = 280;
const miniDrawerWidth = 64;

const AdminLayout = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { user, logout, hasPermission } = useAuthStore();
  const { isDarkMode, toggleTheme, compactMode, toggleCompactMode } = useThemeStore();
  
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationAnchor, setNotificationAnchor] = useState(null);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState({ connected: false });

  // WebSocket connection status
  useEffect(() => {
    const updateConnectionStatus = () => {
      setConnectionStatus(WebSocketService.getConnectionStatus());
    };

    WebSocketService.addEventListener('connect', updateConnectionStatus);
    WebSocketService.addEventListener('disconnect', updateConnectionStatus);
    
    // Initial status
    updateConnectionStatus();

    return () => {
      WebSocketService.removeEventListener('connect', updateConnectionStatus);
      WebSocketService.removeEventListener('disconnect', updateConnectionStatus);
    };
  }, []);

  // Handle notifications
  useEffect(() => {
    const handleNotification = (data) => {
      setNotifications(prev => [data, ...prev.slice(0, 9)]); // Keep last 10
    };

    WebSocketService.addEventListener('notification', handleNotification);
    WebSocketService.addEventListener('alert', handleNotification);

    return () => {
      WebSocketService.removeEventListener('notification', handleNotification);
      WebSocketService.removeEventListener('alert', handleNotification);
    };
  }, []);

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Dashboard,
      path: '/dashboard',
      permission: 'dashboard.view',
    },
    {
      id: 'live-operations',
      label: 'Live Operations',
      icon: LiveTv,
      path: '/live-operations',
      permission: 'operations.view',
      badge: connectionStatus.connected ? 'LIVE' : 'OFFLINE',
      badgeColor: connectionStatus.connected ? 'success' : 'error',
    },
    {
      id: 'fleet',
      label: 'Fleet Management',
      icon: DirectionsCar,
      permission: 'fleet.view',
      children: [
        { label: 'Vehicles', path: '/fleet/vehicles', permission: 'fleet.vehicles.view' },
        { label: 'Maintenance', path: '/fleet/maintenance', permission: 'fleet.maintenance.view' },
        { label: 'Tracking', path: '/fleet/tracking', permission: 'fleet.tracking.view' },
      ],
    },
    {
      id: 'users',
      label: 'User Management',
      icon: People,
      permission: 'users.view',
      children: [
        { label: 'Drivers', path: '/users/drivers', permission: 'users.drivers.view' },
        { label: 'Passengers', path: '/users/passengers', permission: 'users.passengers.view' },
        { label: 'Admins', path: '/users/admins', permission: 'users.admins.view' },
      ],
    },
    {
      id: 'finance',
      label: 'Financial Management',
      icon: Payment,
      permission: 'finance.view',
      children: [
        { label: 'Payments', path: '/finance/payments', permission: 'finance.payments.view' },
        { label: 'Revenue', path: '/finance/revenue', permission: 'finance.revenue.view' },
        { label: 'Reports', path: '/finance/reports', permission: 'finance.reports.view' },
      ],
    },
    {
      id: 'parcels',
      label: 'Parcel Management',
      icon: LocalShipping,
      permission: 'parcels.view',
      children: [
        { label: 'Active Deliveries', path: '/parcels/active', permission: 'parcels.active.view' },
        { label: 'History', path: '/parcels/history', permission: 'parcels.history.view' },
        { label: 'Tracking', path: '/parcels/tracking', permission: 'parcels.tracking.view' },
      ],
    },
    {
      id: 'analytics',
      label: 'Analytics & Reports',
      icon: Analytics,
      permission: 'analytics.view',
      children: [
        { label: 'Performance', path: '/analytics/performance', permission: 'analytics.performance.view' },
        { label: 'Business Intelligence', path: '/analytics/bi', permission: 'analytics.bi.view' },
        { label: 'Custom Reports', path: '/analytics/reports', permission: 'analytics.reports.view' },
      ],
    },
    {
      id: 'settings',
      label: 'System Settings',
      icon: Settings,
      permission: 'settings.view',
      children: [
        { label: 'General', path: '/settings/general', permission: 'settings.general.view' },
        { label: 'Security', path: '/settings/security', permission: 'settings.security.view' },
        { label: 'Integrations', path: '/settings/integrations', permission: 'settings.integrations.view' },
      ],
    },
  ];

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleMenuItemClick = (item) => {
    if (item.children) {
      setExpandedMenus(prev => ({
        ...prev,
        [item.id]: !prev[item.id]
      }));
    } else {
      navigate(item.path);
      if (isMobile) {
        setDrawerOpen(false);
      }
    }
  };

  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderMenuItem = (item, level = 0) => {
    if (!hasPermission(item.permission)) {
      return null;
    }

    const isActive = item.path ? isActiveRoute(item.path) : false;
    const isExpanded = expandedMenus[item.id];

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding sx={{ display: 'block' }}>
          <ListItemButton
            onClick={() => handleMenuItemClick(item)}
            sx={{
              minHeight: 48,
              justifyContent: drawerOpen ? 'initial' : 'center',
              px: 2.5,
              pl: level > 0 ? 4 : 2.5,
              backgroundColor: isActive ? theme.palette.action.selected : 'transparent',
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 0,
                mr: drawerOpen ? 3 : 'auto',
                justifyContent: 'center',
                color: isActive ? theme.palette.primary.main : 'inherit',
              }}
            >
              <item.icon />
            </ListItemIcon>
            <ListItemText
              primary={item.label}
              sx={{
                opacity: drawerOpen ? 1 : 0,
                color: isActive ? theme.palette.primary.main : 'inherit',
              }}
            />
            {drawerOpen && item.badge && (
              <Chip
                label={item.badge}
                size="small"
                color={item.badgeColor || 'default'}
                sx={{ ml: 1 }}
              />
            )}
            {drawerOpen && item.children && (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>
        </ListItem>
        {item.children && (
          <Collapse in={isExpanded && drawerOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: [1],
        }}
      >
        {drawerOpen && (
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            TECNO DRIVE
          </Typography>
        )}
        <IconButton onClick={handleDrawerToggle}>
          {drawerOpen ? <ChevronLeft /> : <ChevronRight />}
        </IconButton>
      </Toolbar>
      <Divider />
      <List sx={{ flexGrow: 1 }}>
        {menuItems.map(item => renderMenuItem(item))}
      </List>
      <Divider />
      <Box sx={{ p: 2 }}>
        {drawerOpen && (
          <FormControlLabel
            control={
              <Switch
                checked={compactMode}
                onChange={toggleCompactMode}
                size="small"
              />
            }
            label="Compact Mode"
            sx={{ mb: 1 }}
          />
        )}
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerOpen ? drawerWidth : miniDrawerWidth}px)` },
          ml: { md: `${drawerOpen ? drawerWidth : miniDrawerWidth}px` },
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => isActiveRoute(item.path))?.label || 'Dashboard'}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Toggle theme">
              <IconButton color="inherit" onClick={() => toggleTheme()}>
                {isDarkMode ? <LightMode /> : <DarkMode />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Notifications">
              <IconButton color="inherit" onClick={handleNotificationClick}>
                <Badge badgeContent={notifications.length} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Account">
              <IconButton color="inherit" onClick={handleMenuClick}>
                <Avatar
                  src={user?.avatar}
                  alt={user?.name}
                  sx={{ width: 32, height: 32 }}
                >
                  {user?.name?.charAt(0)}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerOpen ? drawerWidth : miniDrawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={drawerOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerOpen ? drawerWidth : miniDrawerWidth,
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
              overflowX: 'hidden',
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerOpen ? drawerWidth : miniDrawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: theme.palette.background.default,
        }}
      >
        <Toolbar />
        {children}
      </Box>

      {/* User Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => navigate('/profile')}>
          <ListItemIcon><AccountCircle /></ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={() => navigate('/settings')}>
          <ListItemIcon><Settings /></ListItemIcon>
          Settings
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon><Logout /></ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{ sx: { width: 320, maxHeight: 400 } }}
      >
        {notifications.length === 0 ? (
          <MenuItem>
            <Typography variant="body2" color="text.secondary">
              No new notifications
            </Typography>
          </MenuItem>
        ) : (
          notifications.map((notification, index) => (
            <MenuItem key={index} onClick={handleNotificationClose}>
              <Box>
                <Typography variant="subtitle2">
                  {notification.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {notification.message}
                </Typography>
              </Box>
            </MenuItem>
          ))
        )}
      </Menu>
    </Box>
  );
};

export default AdminLayout;
