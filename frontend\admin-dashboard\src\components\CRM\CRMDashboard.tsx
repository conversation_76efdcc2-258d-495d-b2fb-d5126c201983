import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Avatar,
  LinearProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  Divider,
} from '@mui/material';
import {
  Person,
  Phone,
  Email,
  LocationOn,
  Star,
  TrendingUp,
  TrendingDown,
  Add,
  Edit,
  Delete,
  Visibility,
  Message,
  Call,
  Assignment,
  History,
  Analytics,
  FilterList,
  Search,
  Download,
  Upload,
  Refresh,
  SentimentVeryDissatisfied,
  SentimentDissatisfied,
  SentimentNeutral,
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentVerySatisfied,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  totalRides: number;
  totalSpent: number;
  averageRating: number;
  lastRide: Date;
  status: 'active' | 'inactive' | 'vip';
  joinDate: Date;
  avatar?: string;
  preferredPayment: string;
  notes: string;
}

interface CustomerInteraction {
  id: string;
  customerId: string;
  type: 'call' | 'email' | 'chat' | 'complaint' | 'feedback';
  subject: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo: string;
  createdAt: Date;
  updatedAt: Date;
}

const CRMDashboard: React.FC = () => {
  const theme = useTheme();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [interactions, setInteractions] = useState<CustomerInteraction[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCustomers();
    loadInteractions();
  }, []);

  const loadCustomers = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockCustomers: Customer[] = [
        {
          id: 'cust-001',
          name: 'أحمد محمد السعيد',
          email: '<EMAIL>',
          phone: '+966501234567',
          location: 'الرياض',
          totalRides: 45,
          totalSpent: 1250.75,
          averageRating: 4.8,
          lastRide: new Date(Date.now() - 86400000),
          status: 'vip',
          joinDate: new Date('2023-01-15'),
          preferredPayment: 'Credit Card',
          notes: 'عميل مميز، يفضل السيارات الفاخرة',
        },
        {
          id: 'cust-002',
          name: 'فاطمة علي الزهراني',
          email: '<EMAIL>',
          phone: '+966507654321',
          location: 'جدة',
          totalRides: 23,
          totalSpent: 680.50,
          averageRating: 4.5,
          lastRide: new Date(Date.now() - 172800000),
          status: 'active',
          joinDate: new Date('2023-03-20'),
          preferredPayment: 'Wallet',
          notes: 'تستخدم التطبيق بانتظام للذهاب للعمل',
        },
        {
          id: 'cust-003',
          name: 'محمد عبدالله القحطاني',
          email: '<EMAIL>',
          phone: '+966509876543',
          location: 'الدمام',
          totalRides: 8,
          totalSpent: 195.25,
          averageRating: 3.2,
          lastRide: new Date(Date.now() - 2592000000),
          status: 'inactive',
          joinDate: new Date('2023-08-10'),
          preferredPayment: 'Cash',
          notes: 'لديه شكاوى متكررة حول أوقات الانتظار',
        },
      ];
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadInteractions = async () => {
    try {
      // Mock data - replace with actual API call
      const mockInteractions: CustomerInteraction[] = [
        {
          id: 'int-001',
          customerId: 'cust-001',
          type: 'feedback',
          subject: 'تقييم إيجابي للخدمة',
          description: 'العميل راضي جداً عن جودة الخدمة والسائق المهذب',
          status: 'resolved',
          priority: 'low',
          assignedTo: 'سارة أحمد',
          createdAt: new Date(Date.now() - 3600000),
          updatedAt: new Date(Date.now() - 1800000),
        },
        {
          id: 'int-002',
          customerId: 'cust-003',
          type: 'complaint',
          subject: 'تأخير في الوصول',
          description: 'العميل يشكو من تأخير السائق 15 دقيقة عن الموعد المحدد',
          status: 'in_progress',
          priority: 'medium',
          assignedTo: 'محمد علي',
          createdAt: new Date(Date.now() - 7200000),
          updatedAt: new Date(Date.now() - 3600000),
        },
      ];
      setInteractions(mockInteractions);
    } catch (error) {
      console.error('Error loading interactions:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'vip': return 'warning';
      case 'active': return 'success';
      case 'inactive': return 'error';
      default: return 'default';
    }
  };

  const getSentimentIcon = (rating: number) => {
    if (rating >= 4.5) return <SentimentVerySatisfied color="success" />;
    if (rating >= 4.0) return <SentimentSatisfiedIcon color="success" />;
    if (rating >= 3.0) return <SentimentNeutral color="warning" />;
    if (rating >= 2.0) return <SentimentDissatisfied color="error" />;
    return <SentimentVeryDissatisfied color="error" />;
  };

  const totalCustomers = customers.length;
  const vipCustomers = customers.filter(c => c.status === 'vip').length;
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const totalRevenue = customers.reduce((sum, c) => sum + c.totalSpent, 0);
  const averageRating = customers.reduce((sum, c) => sum + c.averageRating, 0) / customers.length || 0;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          إدارة علاقات العملاء (CRM)
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<Analytics />}>
            التقارير
          </Button>
          <Button variant="outlined" startIcon={<Download />}>
            تصدير
          </Button>
          <Button variant="contained" startIcon={<Add />}>
            عميل جديد
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    إجمالي العملاء
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {totalCustomers}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                  <Person />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    العملاء المميزون
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {vipCustomers}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
                  <Star />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    إجمالي الإيرادات
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {totalRevenue.toLocaleString()} ر.س
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.success.main }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    متوسط التقييم
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {averageRating.toFixed(1)}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                  {getSentimentIcon(averageRating)}
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Customers Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" fontWeight="bold">
              قائمة العملاء
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton>
                <FilterList />
              </IconButton>
              <IconButton>
                <Search />
              </IconButton>
              <IconButton onClick={loadCustomers}>
                <Refresh />
              </IconButton>
            </Box>
          </Box>
          
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>العميل</TableCell>
                  <TableCell>معلومات الاتصال</TableCell>
                  <TableCell>الإحصائيات</TableCell>
                  <TableCell>التقييم</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>آخر رحلة</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <AnimatePresence>
                  {customers.map((customer, index) => (
                    <motion.tr
                      key={customer.id}
                      component={TableRow}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar src={customer.avatar}>
                            {customer.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {customer.name}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              عضو منذ {customer.joinDate.toLocaleDateString('ar-SA')}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Email fontSize="small" color="action" />
                            <Typography variant="body2">{customer.email}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Phone fontSize="small" color="action" />
                            <Typography variant="body2">{customer.phone}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationOn fontSize="small" color="action" />
                            <Typography variant="body2">{customer.location}</Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            <strong>{customer.totalRides}</strong> رحلة
                          </Typography>
                          <Typography variant="body2" color="success.main">
                            {customer.totalSpent.toLocaleString()} ر.س
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Rating value={customer.averageRating} readOnly size="small" />
                          <Typography variant="body2">
                            {customer.averageRating.toFixed(1)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={customer.status === 'vip' ? 'مميز' : customer.status === 'active' ? 'نشط' : 'غير نشط'}
                          color={getStatusColor(customer.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {customer.lastRide.toLocaleDateString('ar-SA')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="عرض التفاصيل">
                            <IconButton 
                              size="small"
                              onClick={() => {
                                setSelectedCustomer(customer);
                                setDialogOpen(true);
                              }}
                            >
                              <Visibility />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="اتصال">
                            <IconButton size="small">
                              <Call />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="رسالة">
                            <IconButton size="small">
                              <Message />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Customer Details Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل العميل</DialogTitle>
        <DialogContent>
          {selectedCustomer && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="الاسم"
                    value={selectedCustomer.name}
                    margin="normal"
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="البريد الإلكتروني"
                    value={selectedCustomer.email}
                    margin="normal"
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="رقم الهاتف"
                    value={selectedCustomer.phone}
                    margin="normal"
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="الموقع"
                    value={selectedCustomer.location}
                    margin="normal"
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="ملاحظات"
                    value={selectedCustomer.notes}
                    margin="normal"
                    multiline
                    rows={3}
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            إغلاق
          </Button>
          <Button variant="contained">
            تعديل
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CRMDashboard;
