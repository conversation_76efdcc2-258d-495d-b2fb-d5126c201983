@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE HYBRID PLATFORM
echo ========================================
echo.
echo Using Docker services + Spring Boot services
echo.

echo [DOCKER SERVICES RUNNING]
echo ✅ Eureka Server (Docker) - Port 8761
echo ✅ Fleet Service (Docker) - Port 8084  
echo ✅ Location Service (Docker) - Port 8085
echo ✅ PostgreSQL Database - Port 5432
echo ✅ pgAdmin - Port 5050
echo.

echo [STARTING SPRING BOOT SERVICES]
echo.

echo [1] Starting API Gateway (Port 8080)...
start "API Gateway" cmd /k "cd microservices\infrastructure\api-gateway && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8080 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 20 /nobreak >nul

echo [2] Starting Auth Service (Port 8081)...
start "Auth Service" cmd /k "cd microservices\core\auth-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8081 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo [3] Starting User Service (Port 8082)...
start "User Service" cmd /k "cd microservices\core\user-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8082 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo [4] Starting Payment Service (Port 8083)...
start "Payment Service" cmd /k "cd microservices\core\payment-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8083 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo [5] Starting Ride Service (Port 8086)...
start "Ride Service" cmd /k "cd microservices\business\ride-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8086 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo [6] Starting Analytics Service (Port 8087)...
start "Analytics Service" cmd /k "cd microservices\business\analytics-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8087 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo [7] Starting Notification Service (Port 8088)...
start "Notification Service" cmd /k "cd microservices\business\notification-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8088 -Deureka.client.service-url.defaultZone=http://localhost:8761/eureka'"
timeout /t 15 /nobreak >nul

echo.
echo ========================================
echo    HYBRID PLATFORM STARTED
echo ========================================
echo.
echo 🐳 DOCKER SERVICES:
echo - Eureka Server: http://localhost:8761
echo - Fleet Service: http://localhost:8084
echo - Location Service: http://localhost:8085
echo - pgAdmin: http://localhost:5050
echo.
echo 🚀 SPRING BOOT SERVICES:
echo - API Gateway: http://localhost:8080
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8082
echo - Payment Service: http://localhost:8083
echo - Ride Service: http://localhost:8086
echo - Analytics Service: http://localhost:8087
echo - Notification Service: http://localhost:8088
echo.
echo Run hybrid-health-check.bat to verify all services
echo.
pause
