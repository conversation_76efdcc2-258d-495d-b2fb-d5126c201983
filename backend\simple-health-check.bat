@echo off
echo ========================================
echo    TECNO DRIVE PLATFORM STATUS
echo ========================================
echo.

echo DOCKER SERVICES:
echo.

echo [1] Eureka Server (Docker - 8761):
curl -s http://localhost:8761 | findstr "Eureka" >nul
if %errorlevel% equ 0 (
    echo HEALTHY - Service Discovery running
) else (
    echo UNHEALTHY - Not responding
)

echo [2] Fleet Service (Docker - 8084):
curl -s http://localhost:8084 | findstr "Fleet" >nul
if %errorlevel% equ 0 (
    echo HEALTHY - Fleet service running
) else (
    echo UNHEALTHY - Not responding
)

echo [3] Location Service (Docker - 8085):
curl -s http://localhost:8085 | findstr "Location" >nul
if %errorlevel% equ 0 (
    echo HEALTHY - Location service running
) else (
    echo UNHEALTHY - Not responding
)

echo.
echo SPRING BOOT SERVICES:
echo.

echo [4] API Gateway (8080):
curl -s http://localhost:8080/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - API Gateway running
) else (
    echo UNHEALTHY - Not responding
)

echo [5] Auth Service (8081):
curl -s http://localhost:8081/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Auth service running
) else (
    echo UNHEALTHY - Not responding
)

echo [6] User Service (8082):
curl -s http://localhost:8082/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - User service running
) else (
    echo UNHEALTHY - Not responding
)

echo [7] Payment Service (8083):
curl -s http://localhost:8083/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Payment service running
) else (
    echo UNHEALTHY - Not responding
)

echo.
echo ========================================
echo    PLATFORM ACCESS POINTS
echo ========================================
echo.
echo - Eureka Dashboard: http://localhost:8761
echo - API Gateway: http://localhost:8080
echo - pgAdmin: http://localhost:5050
echo.
pause
