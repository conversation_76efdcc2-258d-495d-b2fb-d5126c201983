import { Client } from '@stomp/stompjs';
import Sock<PERSON><PERSON> from 'sockjs-client';

class WebSocketService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.subscriptions = new Map();
  }

  connect() {
    if (this.connected) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      // Create STOMP client with SockJS
      this.client = new Client({
        webSocketFactory: () => new SockJS('http://localhost:8100/ws'),
        connectHeaders: {},
        debug: (str) => {
          console.log('STOMP Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
      });

      // Connection success callback
      this.client.onConnect = (frame) => {
        console.log('Connected to WebSocket:', frame);
        this.connected = true;
        resolve();
      };

      // Connection error callback
      this.client.onStompError = (frame) => {
        console.error('STOMP Error:', frame);
        this.connected = false;
        reject(new Error('WebSocket connection failed'));
      };

      // Web socket error callback
      this.client.onWebSocketError = (error) => {
        console.error('WebSocket Error:', error);
        this.connected = false;
        reject(error);
      };

      // Disconnect callback
      this.client.onDisconnect = () => {
        console.log('Disconnected from WebSocket');
        this.connected = false;
      };

      // Activate the client
      this.client.activate();
    });
  }

  disconnect() {
    if (this.client && this.connected) {
      // Unsubscribe from all subscriptions
      this.subscriptions.forEach((subscription) => {
        subscription.unsubscribe();
      });
      this.subscriptions.clear();

      // Deactivate the client
      this.client.deactivate();
      this.connected = false;
    }
  }

  subscribe(destination, callback) {
    if (!this.connected || !this.client) {
      console.error('WebSocket not connected. Cannot subscribe to:', destination);
      return null;
    }

    try {
      const subscription = this.client.subscribe(destination, (message) => {
        try {
          const data = JSON.parse(message.body);
          callback(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          callback(message.body);
        }
      });

      this.subscriptions.set(destination, subscription);
      console.log('Subscribed to:', destination);
      return subscription;
    } catch (error) {
      console.error('Error subscribing to:', destination, error);
      return null;
    }
  }

  unsubscribe(destination) {
    const subscription = this.subscriptions.get(destination);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(destination);
      console.log('Unsubscribed from:', destination);
    }
  }

  send(destination, data) {
    if (!this.connected || !this.client) {
      console.error('WebSocket not connected. Cannot send to:', destination);
      return false;
    }

    try {
      this.client.publish({
        destination: destination,
        body: JSON.stringify(data),
      });
      return true;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }

  isConnected() {
    return this.connected;
  }
}

// Export singleton instance
export default new WebSocketService();
