import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress, Typography } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Layout Components
import AdminLayout from './components/layout/AdminLayout';
import LoginPage from './pages/auth/LoginPage';

// Page Components
import Dashboard from './pages/Dashboard';
import LiveOperations from './pages/operations/LiveOperations';
import UserManagement from './pages/users/UserManagement';
import AdvancedUserManagement from './pages/users/AdvancedUserManagement';
import FleetManagement from './pages/fleet/FleetManagement';
import AdvancedFleetManagement from './pages/fleet/AdvancedFleetManagement';
import FinancialManagement from './pages/finance/FinancialManagement';
import AdvancedFinancialManagement from './pages/finance/AdvancedFinancialManagement';
import ParcelManagement from './pages/parcels/ParcelManagement';
import Analytics from './pages/analytics/Analytics';
import SystemSettings from './pages/settings/SystemSettings';
import NotificationCenter from './components/notifications/NotificationCenter';

// Services
import { WebSocketService } from './services/WebSocketService';

// Store
import { useAuthStore } from './store/authStore';
import { useThemeStore } from './store/themeStore';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuthStore();
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Loading Component
const LoadingScreen = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      gap: 2,
    }}
  >
    <CircularProgress size={60} />
    <Typography variant="h6" color="text.secondary">
      Loading TECNO DRIVE...
    </Typography>
  </Box>
);

function App() {
  const { isAuthenticated, initializeAuth } = useAuthStore();
  const { isDarkMode, toggleTheme } = useThemeStore();
  const [isLoading, setIsLoading] = useState(true);

  // Create dynamic theme
  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
      background: {
        default: isDarkMode ? '#121212' : '#f5f5f5',
        paper: isDarkMode ? '#1e1e1e' : '#ffffff',
      },
      success: {
        main: '#2e7d32',
      },
      warning: {
        main: '#ed6c02',
      },
      error: {
        main: '#d32f2f',
      },
      info: {
        main: '#0288d1',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 600,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 600,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 500,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 500,
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 500,
      },
    },
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: isDarkMode 
              ? '0 4px 6px rgba(0, 0, 0, 0.3)' 
              : '0 4px 6px rgba(0, 0, 0, 0.1)',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 500,
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 12,
          },
        },
      },
    },
  });

  useEffect(() => {
    const initApp = async () => {
      try {
        // Initialize authentication
        await initializeAuth();
        
        // Initialize WebSocket if authenticated
        if (isAuthenticated) {
          WebSocketService.connect();
        }
      } catch (error) {
        console.error('App initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initApp();

    // Cleanup on unmount
    return () => {
      WebSocketService.disconnect();
    };
  }, [initializeAuth, isAuthenticated]);

  // Auto theme detection
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e) => {
      if (!localStorage.getItem('themePreference')) {
        toggleTheme(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [toggleTheme]);

  if (isLoading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          bgcolor="background.default"
        >
          <div className="loading-spinner">Loading TecnoDrive Admin...</div>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            {isAuthenticated ? (
              <AdminLayout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/live-operations" element={<LiveOperations />} />
                  <Route path="/users" element={<UserManagement />} />
                  <Route path="/users/advanced" element={<AdvancedUserManagement />} />
                  <Route path="/fleet" element={<FleetManagement />} />
                  <Route path="/fleet/advanced" element={<AdvancedFleetManagement />} />
                  <Route path="/finance" element={<FinancialManagement />} />
                  <Route path="/finance/advanced" element={<AdvancedFinancialManagement />} />
                  <Route path="/parcels/*" element={<ParcelManagement />} />
                  <Route path="/analytics/*" element={<Analytics />} />
                  <Route path="/settings/*" element={<SystemSettings />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </AdminLayout>
            ) : (
              <Routes>
                <Route path="/login" element={<LoginPage />} />
                <Route path="*" element={<Navigate to="/login" replace />} />
              </Routes>
            )}
          </Box>
        </Router>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: isDarkMode ? '#333' : '#fff',
              color: isDarkMode ? '#fff' : '#333',
            },
          }}
        />
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
