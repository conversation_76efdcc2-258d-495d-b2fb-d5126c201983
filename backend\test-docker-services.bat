@echo off
echo ========================================
echo    TESTING DOCKER SERVICES
echo ========================================
echo.

echo [1] Testing Eureka Server (Docker - Port 8761):
curl -s -w "Status: %%{http_code}\n" http://localhost:8761 -o nul
curl -s http://localhost:8761

echo.
echo [2] Testing Fleet Service (Docker - Port 8084):
curl -s -w "Status: %%{http_code}\n" http://localhost:8084 -o nul
curl -s http://localhost:8084

echo.
echo [3] Testing Location Service (Docker - Port 8085):
curl -s -w "Status: %%{http_code}\n" http://localhost:8085 -o nul
curl -s http://localhost:8085

echo.
echo [4] Testing pgAdmin (Port 5050):
curl -s -w "Status: %%{http_code}\n" http://localhost:5050 -o nul

echo.
echo [5] Docker Container Status:
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo ========================================
echo    DOCKER SERVICES TEST COMPLETE
echo ========================================
