import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  LocalShipping,
  DirectionsCar,
  Schedule,
  CheckCircle,
  Warning,
  Cancel,
  Map,
  Assignment,
} from '@mui/icons-material';

const Rides: React.FC = () => {
  const recentRides = [
    {
      id: 'RID-001',
      customer: '<PERSON>',
      driver: '<PERSON>',
      pickup: 'Riyadh Mall',
      destination: 'King Fahd Airport',
      status: 'completed',
      fare: 45.50,
      time: '14:30',
    },
    {
      id: 'RID-002',
      customer: '<PERSON><PERSON>',
      driver: '<PERSON>',
      pickup: 'Al-Nakheel Mall',
      destination: 'King Saud University',
      status: 'in-progress',
      fare: 28.75,
      time: '15:15',
    },
    {
      id: 'RID-003',
      customer: '<PERSON>',
      driver: '<PERSON>',
      pickup: 'Olaya District',
      destination: 'Diplomatic Quarter',
      status: 'pending',
      fare: 32.00,
      time: '15:45',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'primary';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle />;
      case 'in-progress':
        return <DirectionsCar />;
      case 'pending':
        return <Schedule />;
      case 'cancelled':
        return <Cancel />;
      default:
        return <Assignment />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Ride & Delivery Management
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DirectionsCar color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Active Rides</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                24
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Currently in progress
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocalShipping color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Deliveries</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                18
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Packages in transit
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Schedule color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Pending</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                12
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Awaiting assignment
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckCircle color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Completed Today</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                156
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total completed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Recent Rides & Deliveries
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button variant="outlined" startIcon={<Map />}>
                    Live Map
                  </Button>
                  <Button variant="contained" startIcon={<Assignment />}>
                    Assign Ride
                  </Button>
                </Box>
              </Box>
              
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Ride ID</TableCell>
                      <TableCell>Customer</TableCell>
                      <TableCell>Driver</TableCell>
                      <TableCell>Route</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Fare</TableCell>
                      <TableCell>Time</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentRides.map((ride) => (
                      <TableRow key={ride.id}>
                        <TableCell>{ride.id}</TableCell>
                        <TableCell>{ride.customer}</TableCell>
                        <TableCell>{ride.driver}</TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              From: {ride.pickup}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              To: {ride.destination}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            icon={getStatusIcon(ride.status)}
                            label={ride.status.replace('-', ' ')}
                            color={getStatusColor(ride.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>SAR {ride.fare}</TableCell>
                        <TableCell>{ride.time}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Rides;
