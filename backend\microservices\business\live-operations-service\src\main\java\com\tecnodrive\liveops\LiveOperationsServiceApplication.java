package com.tecnodrive.liveops;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

/**
 * Live Operations Service Application
 * Real-time operations monitoring and management with dashboard
 *
 * Features:
 * - Real-time fleet monitoring
 * - Live ride tracking
 * - Instant alerts and notifications
 * - Operations dashboard
 * - WebSocket for real-time updates
 */
@SpringBootApplication
@EnableEurekaClient
@EnableFeignClients
@EnableAsync
@EnableScheduling
@EnableWebSocket
public class LiveOperationsServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(LiveOperationsServiceApplication.class, args);
    }
}
