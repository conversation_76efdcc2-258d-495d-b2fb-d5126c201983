@echo off
echo ========================================
echo    TECNO DRIVE API TESTING
echo ========================================
echo.

echo Testing APIs through API Gateway (Port 8080):
echo.

echo [1] Testing Auth Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/auth/actuator/health -o nul

echo [2] Testing User Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/users/actuator/health -o nul

echo [3] Testing Payment Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/payments/actuator/health -o nul

echo [4] Testing Ride Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/rides/actuator/health -o nul

echo [5] Testing Fleet Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/fleet/actuator/health -o nul

echo [6] Testing Location Service through Gateway:
curl -s -w "Status: %%{http_code}\n" http://localhost:8080/location/actuator/health -o nul

echo.
echo Testing Direct Service APIs:
echo.

echo [7] Auth Service Info:
curl -s http://localhost:8081/actuator/info

echo.
echo [8] User Service Info:
curl -s http://localhost:8082/actuator/info

echo.
echo [9] Ride Service Info:
curl -s http://localhost:8084/actuator/info

echo.
echo ========================================
echo    API TESTING COMPLETE
echo ========================================
