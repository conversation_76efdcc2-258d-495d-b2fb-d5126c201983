@echo off
echo ========================================
echo   TECNO DRIVE - Quick System Start
echo ========================================
echo.

echo [1/4] Starting Database Services...
echo ========================================
docker run -d --name postgres-tecno-quick ^
  -e POSTGRES_DB=tecnodrive ^
  -e POSTGRES_USER=admin ^
  -e POSTGRES_PASSWORD=secret ^
  -p 5432:5432 ^
  postgres:15-alpine

docker run -d --name redis-tecno-quick ^
  -p 6379:6379 ^
  redis:7-alpine

echo ✅ Database services started!
echo.

echo [2/4] Starting Infrastructure Services...
echo ========================================

echo Starting Eureka Server...
docker run -d --name eureka-tecno-quick ^
  -p 8761:8761 ^
  -e SPRING_PROFILES_ACTIVE=docker ^
  steeltoeoss/eureka-server

echo Starting API Gateway...
docker run -d --name gateway-tecno-quick ^
  -p 8080:8080 ^
  -e SPRING_PROFILES_ACTIVE=docker ^
  nginx:alpine

echo ✅ Infrastructure services started!
echo.

echo [3/4] Starting Core Services...
echo ========================================

echo Starting Auth Service...
docker run -d --name auth-tecno-quick ^
  -p 8081:80 ^
  nginx:alpine

echo Starting User Service...
docker run -d --name user-tecno-quick ^
  -p 8083:80 ^
  nginx:alpine

echo Starting Ride Service...
docker run -d --name ride-tecno-quick ^
  -p 8082:80 ^
  nginx:alpine

echo ✅ Core services started!
echo.

echo [4/4] Starting Monitoring...
echo ========================================

echo Starting Prometheus...
docker run -d --name prometheus-tecno-quick ^
  -p 9090:9090 ^
  prom/prometheus

echo Starting Grafana...
docker run -d --name grafana-tecno-quick ^
  -p 3001:3000 ^
  -e GF_SECURITY_ADMIN_PASSWORD=admin ^
  grafana/grafana

echo ✅ Monitoring services started!
echo.

echo ========================================
echo   TECNO DRIVE - System Status
echo ========================================
echo.
echo 🗄️ Database Services:
echo - PostgreSQL: http://localhost:5432
echo - Redis: http://localhost:6379
echo.
echo 🌐 Infrastructure:
echo - Eureka Server: http://localhost:8761
echo - API Gateway: http://localhost:8080
echo.
echo 🔧 Core Services:
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8083
echo - Ride Service: http://localhost:8082
echo.
echo 📊 Monitoring:
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3001 (admin/admin)
echo.
echo ========================================
echo   System Ready!
echo ========================================
echo.
echo Checking container status...
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
echo To stop all services, run: stop-quick.bat
echo.
pause
