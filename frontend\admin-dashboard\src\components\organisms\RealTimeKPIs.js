import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  Skeleton
} from '@mui/material';
import {
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import numeral from 'numeral';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

const RealTimeKPIs = ({ kpis = {}, isLoading = false }) => {
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [trendData, setTrendData] = useState({});

  // Mock trend data - في التطبيق الحقيقي سيأتي من API
  useEffect(() => {
    const generateTrendData = (baseValue, variance = 0.1) => {
      const data = [];
      const labels = [];
      const now = new Date();
      
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        labels.push(time.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }));
        
        const variation = (Math.random() - 0.5) * variance;
        data.push(Math.max(0, baseValue * (1 + variation)));
      }
      
      return { labels, data };
    };

    setTrendData({
      activeRides: generateTrendData(kpis.activeRides || 45, 0.3),
      availableDrivers: generateTrendData(kpis.availableDrivers || 23, 0.2),
      revenue: generateTrendData(kpis.revenue || 2450, 0.4),
      avgWaitTime: generateTrendData(kpis.avgWaitTime || 3.2, 0.15)
    });
  }, [kpis]);

  const kpiCards = [
    {
      id: 'activeRides',
      title: 'الرحلات النشطة',
      value: kpis.activeRides || 0,
      icon: <CarIcon />,
      color: '#4caf50',
      trend: kpis.activeRidesTrend || 12,
      subtitle: 'رحلة جارية',
      target: 60,
      unit: 'رحلة'
    },
    {
      id: 'availableDrivers',
      title: 'السائقين المتاحين',
      value: kpis.availableDrivers || 0,
      icon: <PersonIcon />,
      color: '#2196f3',
      trend: kpis.availableDriversTrend || -5,
      subtitle: 'سائق متاح',
      target: 30,
      unit: 'سائق'
    },
    {
      id: 'revenue',
      title: 'إيرادات اليوم',
      value: kpis.revenue || 0,
      icon: <MoneyIcon />,
      color: '#ff9800',
      trend: kpis.revenueTrend || 18,
      subtitle: 'ريال سعودي',
      target: 5000,
      unit: 'ر.س',
      format: 'currency'
    },
    {
      id: 'avgWaitTime',
      title: 'متوسط وقت الانتظار',
      value: kpis.avgWaitTime || 0,
      icon: <ScheduleIcon />,
      color: '#9c27b0',
      trend: kpis.avgWaitTimeTrend || -8,
      subtitle: 'دقيقة',
      target: 5,
      unit: 'دقيقة',
      inverse: true // Lower is better
    }
  ];

  const formatValue = (value, format, unit) => {
    if (format === 'currency') {
      return numeral(value).format('0,0') + ' ' + unit;
    }
    return numeral(value).format('0,0') + ' ' + unit;
  };

  const getTrendIcon = (trend, inverse = false) => {
    const isPositive = inverse ? trend < 0 : trend > 0;
    if (Math.abs(trend) < 1) return <RemoveIcon fontSize="small" />;
    return isPositive ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />;
  };

  const getTrendColor = (trend, inverse = false) => {
    const isPositive = inverse ? trend < 0 : trend > 0;
    if (Math.abs(trend) < 1) return '#9e9e9e';
    return isPositive ? '#4caf50' : '#f44336';
  };

  const getProgressValue = (value, target) => {
    return Math.min((value / target) * 100, 100);
  };

  const createChartData = (metric) => {
    const trend = trendData[metric];
    if (!trend) return null;

    return {
      labels: trend.labels,
      datasets: [
        {
          label: kpiCards.find(k => k.id === metric)?.title || '',
          data: trend.data,
          borderColor: kpiCards.find(k => k.id === metric)?.color || '#2196f3',
          backgroundColor: `${kpiCards.find(k => k.id === metric)?.color || '#2196f3'}20`,
          fill: true,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
        },
      ],
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
      },
    },
    elements: {
      line: {
        borderWidth: 2,
      },
    },
  };

  if (isLoading) {
    return (
      <Grid container spacing={2}>
        {[1, 2, 3, 4].map((item) => (
          <Grid item xs={12} sm={6} md={3} key={item}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="60%" height={24} />
                <Skeleton variant="text" width="40%" height={32} />
                <Skeleton variant="rectangular" width="100%" height={60} />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  return (
    <Grid container spacing={2}>
      {kpiCards.map((kpi) => (
        <Grid item xs={12} sm={6} md={3} key={kpi.id}>
          <Card 
            sx={{ 
              height: '100%',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4,
              },
              border: selectedMetric === kpi.id ? `2px solid ${kpi.color}` : 'none'
            }}
            onClick={() => setSelectedMetric(selectedMetric === kpi.id ? null : kpi.id)}
          >
            <CardContent sx={{ pb: 1 }}>
              {/* Header */}
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {kpi.title}
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="h4" fontWeight="bold" color={kpi.color}>
                      {formatValue(kpi.value, kpi.format, kpi.unit)}
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: `${kpi.color}20`, color: kpi.color, width: 40, height: 40 }}>
                  {kpi.icon}
                </Avatar>
              </Box>

              {/* Trend */}
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Chip
                  icon={getTrendIcon(kpi.trend, kpi.inverse)}
                  label={`${Math.abs(kpi.trend)}%`}
                  size="small"
                  sx={{
                    backgroundColor: `${getTrendColor(kpi.trend, kpi.inverse)}20`,
                    color: getTrendColor(kpi.trend, kpi.inverse),
                    fontWeight: 'bold'
                  }}
                />
                <Typography variant="caption" color="textSecondary">
                  آخر 24 ساعة
                </Typography>
              </Box>

              {/* Progress */}
              <Box mb={1}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                  <Typography variant="caption" color="textSecondary">
                    الهدف: {formatValue(kpi.target, kpi.format, kpi.unit)}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    {Math.round(getProgressValue(kpi.value, kpi.target))}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={getProgressValue(kpi.value, kpi.target)}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: `${kpi.color}20`,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: kpi.color,
                      borderRadius: 3,
                    },
                  }}
                />
              </Box>

              {/* Mini Chart */}
              {selectedMetric === kpi.id && trendData[kpi.id] && (
                <Box height={60} mt={1}>
                  <Line data={createChartData(kpi.id)} options={chartOptions} />
                </Box>
              )}

              {/* Actions */}
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                <Typography variant="caption" color="textSecondary">
                  {kpi.subtitle}
                </Typography>
                <Box>
                  <Tooltip title="عرض التفاصيل">
                    <IconButton size="small">
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="تحديث">
                    <IconButton size="small">
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default RealTimeKPIs;
