@echo off
echo ========================================
echo   TECNO DRIVE - Container Management
echo ========================================
echo.

:menu
echo Please select an option:
echo.
echo [1] Start Basic Services (7 containers)
echo [2] Start Full System (20+ containers)
echo [3] Start Monitoring Only (10 containers)
echo [4] Stop All Containers
echo [5] View Container Status
echo [6] Clean Up (Remove all containers and volumes)
echo [0] Exit
echo.
set /p choice="Enter your choice (0-6): "

if "%choice%"=="1" goto basic
if "%choice%"=="2" goto full
if "%choice%"=="3" goto monitoring
if "%choice%"=="4" goto stop
if "%choice%"=="5" goto status
if "%choice%"=="6" goto cleanup
if "%choice%"=="0" goto exit
goto menu

:basic
echo.
echo ========================================
echo   Starting Basic Services (7 containers)
echo ========================================
echo.
echo Starting:
echo - tecnodrive-postgres (PostgreSQL Database)
echo - tecnodrive-redis (Redis Cache)
echo - tecnodrive-eureka (Service Discovery)
echo - tecnodrive-api-gateway (API Gateway)
echo - tecnodrive-auth-service (Authentication Service)
echo - tecnodrive-user-service (User Management)
echo - tecnodrive-ride-service (Ride Management)
echo.

docker-compose -f docker-compose.yml up -d postgres redis eureka-server api-gateway auth-service user-service ride-service

echo.
echo ✅ Basic services started successfully!
echo.
echo Access URLs:
echo - Eureka Dashboard: http://localhost:8761
echo - API Gateway: http://localhost:8080
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8082
echo - Ride Service: http://localhost:8083
echo.
pause
goto menu

:full
echo.
echo ========================================
echo   Starting Full System (20+ containers)
echo ========================================
echo.
echo This will start ALL TecnoDrive services including:
echo - All database services
echo - All microservices
echo - Monitoring stack
echo - Management tools
echo.
echo ⚠️  This requires significant system resources!
echo.
set /p confirm="Continue? (y/n): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Starting full system...
docker-compose -f docker-compose.full.yml up -d
docker-compose -f docker-compose.monitoring.yml up -d

echo.
echo ✅ Full system started successfully!
echo.
echo 🌐 Main Services:
echo - API Gateway: http://localhost:8080
echo - Eureka Dashboard: http://localhost:8761
echo.
echo 🗄️ Database Management:
echo - PgAdmin: http://localhost:5050
echo - Redis Insight: http://localhost:8001
echo.
echo 📊 Monitoring:
echo - Grafana: http://localhost:3001
echo - Prometheus: http://localhost:9090
echo - Kibana: http://localhost:5601
echo - Jaeger: http://localhost:16686
echo.
echo 🔧 Microservices:
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8083
echo - Ride Service: http://localhost:8082
echo - Payment Service: http://localhost:8086
echo - Parcel Service: http://localhost:8087
echo - Fleet Service: http://localhost:8084
echo - Location Service: http://localhost:8085
echo - Analytics Service: http://localhost:8088
echo - Notifications: http://localhost:8089
echo - HR Service: http://localhost:8097
echo - Financial Service: http://localhost:8098
echo - Wallet Service: http://localhost:8099
echo - Live Operations: http://localhost:8100
echo - Operations: http://localhost:8101
echo - Tracking: http://localhost:8102
echo - Demand Analysis: http://localhost:8103
echo.
pause
goto menu

:monitoring
echo.
echo ========================================
echo   Starting Monitoring Stack Only
echo ========================================
echo.
docker-compose -f docker-compose.monitoring.yml up -d

echo.
echo ✅ Monitoring services started!
echo.
echo Access URLs:
echo - PgAdmin: http://localhost:5050
echo - Redis Insight: http://localhost:8001
echo - Grafana: http://localhost:3001
echo - Prometheus: http://localhost:9090
echo - Kibana: http://localhost:5601
echo - Jaeger: http://localhost:16686
echo - RabbitMQ Management: http://localhost:15672
echo.
pause
goto menu

:stop
echo.
echo ========================================
echo   Stopping All Containers
echo ========================================
echo.
docker-compose -f docker-compose.yml down
docker-compose -f docker-compose.full.yml down
docker-compose -f docker-compose.monitoring.yml down

echo.
echo ✅ All containers stopped!
echo.
pause
goto menu

:status
echo.
echo ========================================
echo   Container Status
echo ========================================
echo.
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
echo.
echo ========================================
echo   Resource Usage
echo ========================================
echo.
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
echo.
pause
goto menu

:cleanup
echo.
echo ========================================
echo   ⚠️  CLEANUP WARNING ⚠️
echo ========================================
echo.
echo This will:
echo - Stop all containers
echo - Remove all containers
echo - Remove all volumes (DATA WILL BE LOST!)
echo - Remove all networks
echo.
set /p confirm="Are you sure? This cannot be undone! (yes/no): "
if /i not "%confirm%"=="yes" goto menu

echo.
echo Cleaning up...
docker-compose -f docker-compose.yml down -v --remove-orphans
docker-compose -f docker-compose.full.yml down -v --remove-orphans
docker-compose -f docker-compose.monitoring.yml down -v --remove-orphans

echo.
echo Removing unused images...
docker image prune -f

echo.
echo ✅ Cleanup completed!
echo.
pause
goto menu

:exit
echo.
echo Thank you for using TecnoDrive Container Management!
echo.
exit /b 0
