import axios from 'axios';
import { toast } from 'react-hot-toast';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth-token');
      localStorage.removeItem('auth-storage');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please check your connection.');
    }
    return Promise.reject(error);
  }
);

export class AuthService {
  // Mock data for development
  static mockUsers = [
    {
      id: 1,
      email: '<EMAIL>',
      password: 'admin123',
      name: 'مدير النظام',
      role: 'admin',
      permissions: ['all'],
      avatar: null,
      department: 'إدارة النظام',
      phone: '+966501234567'
    },
    {
      id: 2,
      email: '<EMAIL>',
      password: 'manager123',
      name: 'مدير العمليات',
      role: 'manager',
      permissions: ['operations', 'fleet', 'reports'],
      avatar: null,
      department: 'العمليات',
      phone: '+966507654321'
    },
    {
      id: 3,
      email: '<EMAIL>',
      password: 'user123',
      name: 'موظف العمليات',
      role: 'user',
      permissions: ['operations'],
      avatar: null,
      department: 'العمليات',
      phone: '+966509876543'
    }
  ];

  // Check if we should use mock data
  static shouldUseMock() {
    return true; // Force mock for now
  }

  // Mock login function
  static async mockLogin(credentials) {
    console.log('🔵 Mock Login attempt:', credentials);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simple validation - accept admin credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      const user = this.mockUsers[0]; // Use first user (admin)
      console.log('🔍 Using admin user:', user);
    } else {
      console.error('❌ Invalid credentials');
      throw new Error('بيانات تسجيل الدخول غير صحيحة');
    }

    const user = this.mockUsers[0];

    const token = `mock-token-${user.id}-${Date.now()}`;
    const refreshToken = `mock-refresh-token-${user.id}-${Date.now()}`;

    // Store tokens
    localStorage.setItem('auth-token', token);
    localStorage.setItem('refresh-token', refreshToken);

    const result = {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          department: user.department,
          phone: user.phone
        },
        token,
        refreshToken,
        permissions: user.permissions,
      }
    };

    console.log('✅ Mock login successful:', result);
    return result;
  }

  // Authentication methods
  static async login(credentials) {
    // Try mock first if in development or if API fails
    if (this.shouldUseMock()) {
      try {
        return await this.mockLogin(credentials);
      } catch (error) {
        throw new Error(error.message);
      }
    }

    try {
      const response = await api.post('/api/v1/auth/login', credentials);
      const { token, refreshToken, user, permissions } = response.data;

      // Store tokens
      localStorage.setItem('auth-token', token);
      localStorage.setItem('refresh-token', refreshToken);

      return {
        data: {
          user,
          token,
          refreshToken,
          permissions: permissions || [],
        }
      };
    } catch (error) {
      // Fallback to mock if API fails
      console.warn('API login failed, falling back to mock:', error.message);
      try {
        return await this.mockLogin(credentials);
      } catch (mockError) {
        throw this.handleError(error);
      }
    }
  }

  static async logout() {
    try {
      if (!this.shouldUseMock()) {
        const refreshToken = localStorage.getItem('refresh-token');
        if (refreshToken) {
          await api.post('/auth/logout', { refreshToken });
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call result
      localStorage.removeItem('auth-token');
      localStorage.removeItem('refresh-token');
      localStorage.removeItem('auth-storage');
    }
  }

  static async refreshToken(refreshToken) {
    try {
      const response = await api.post('/auth/refresh', { refreshToken });
      const { token, refreshToken: newRefreshToken } = response.data;
      
      localStorage.setItem('auth-token', token);
      localStorage.setItem('refresh-token', newRefreshToken);
      
      return {
        data: {
          token,
          refreshToken: newRefreshToken,
        }
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async verifyToken(token) {
    try {
      const response = await api.post('/auth/verify', { token });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async register(userData) {
    try {
      const response = await api.post('/auth/register', userData);
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async forgotPassword(email) {
    try {
      const response = await api.post('/auth/forgot-password', { email });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async resetPassword(token, newPassword) {
    try {
      const response = await api.post('/auth/reset-password', {
        token,
        newPassword,
      });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async changePassword(passwordData) {
    try {
      const response = await api.post('/auth/change-password', passwordData);
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Profile management
  static async getProfile() {
    try {
      const response = await api.get('/auth/profile');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async updateProfile(profileData) {
    try {
      const response = await api.put('/auth/profile', profileData);
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async uploadAvatar(file) {
    try {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await api.post('/auth/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Two-factor authentication
  static async enableTwoFactor() {
    try {
      const response = await api.post('/auth/2fa/enable');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async disableTwoFactor(code) {
    try {
      const response = await api.post('/auth/2fa/disable', { code });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async verifyTwoFactor(code) {
    try {
      const response = await api.post('/auth/2fa/verify', { code });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Session management
  static async getSessions() {
    try {
      const response = await api.get('/auth/sessions');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async revokeSession(sessionId) {
    try {
      const response = await api.delete(`/auth/sessions/${sessionId}`);
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async revokeAllSessions() {
    try {
      const response = await api.delete('/auth/sessions');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Permissions and roles
  static async getPermissions() {
    try {
      const response = await api.get('/auth/permissions');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  static async getRoles() {
    try {
      const response = await api.get('/auth/roles');
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Audit logs
  static async getAuditLogs(params = {}) {
    try {
      const response = await api.get('/auth/audit-logs', { params });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Utility methods
  static handleError(error) {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    const status = error.response?.status;
    const code = error.response?.data?.code;
    
    return {
      message,
      status,
      code,
      response: error.response,
    };
  }

  static isTokenExpired(token) {
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  static getTokenPayload(token) {
    if (!token) return null;
    
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (error) {
      return null;
    }
  }

  static hasPermission(userPermissions, requiredPermission) {
    if (!userPermissions || !Array.isArray(userPermissions)) return false;
    return userPermissions.includes(requiredPermission) || userPermissions.includes('admin');
  }

  static hasRole(userRoles, requiredRole) {
    if (!userRoles) return false;
    if (Array.isArray(userRoles)) {
      return userRoles.includes(requiredRole);
    }
    return userRoles === requiredRole;
  }
}

export default AuthService;
