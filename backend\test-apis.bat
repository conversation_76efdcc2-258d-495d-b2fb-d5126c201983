@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE API TESTING
echo ========================================
echo.

echo Testing Core APIs...
echo.

:: Test Auth Service
echo [1] Testing Auth Service...
curl -s -X GET http://localhost:8081/api/auth/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8081/api/auth/info -w "Auth Info: %%{http_code}\n"

echo.

:: Test User Service
echo [2] Testing User Service...
curl -s -X GET http://localhost:8082/api/users/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8082/api/users/count -w "User Count: %%{http_code}\n"

echo.

:: Test Payment Service
echo [3] Testing Payment Service...
curl -s -X GET http://localhost:8083/api/payments/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8083/api/payments/methods -w "Payment Methods: %%{http_code}\n"

echo.

echo Testing Business APIs...
echo.

:: Test Ride Service
echo [4] Testing Ride Service...
curl -s -X GET http://localhost:8084/api/rides/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8084/api/rides/available -w "Available Rides: %%{http_code}\n"

echo.

:: Test Fleet Service
echo [5] Testing Fleet Service...
curl -s -X GET http://localhost:8085/api/fleet/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8085/api/fleet/vehicles -w "Fleet Vehicles: %%{http_code}\n"

echo.

:: Test Location Service
echo [6] Testing Location Service...
curl -s -X GET http://localhost:8086/api/location/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8086/api/location/zones -w "Location Zones: %%{http_code}\n"

echo.

:: Test Analytics Service
echo [7] Testing Analytics Service...
curl -s -X GET http://localhost:8087/api/analytics/health -w "Status: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8087/api/analytics/dashboard -w "Analytics Dashboard: %%{http_code}\n"

echo.

:: Test through API Gateway
echo Testing through API Gateway...
echo.

echo [8] Testing Gateway Routes...
curl -s -X GET http://localhost:8080/auth/api/auth/health -w "Gateway->Auth: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8080/users/api/users/health -w "Gateway->Users: %%{http_code}\n" -o nul
curl -s -X GET http://localhost:8080/rides/api/rides/health -w "Gateway->Rides: %%{http_code}\n" -o nul

echo.
echo ========================================
echo    API TESTING COMPLETED
echo ========================================
echo.
echo Check the status codes above:
echo - 200: Success
echo - 404: Endpoint not found
echo - 500: Internal server error
echo - Connection refused: Service not running
echo.
pause
