import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Refresh,
  LocationOn,
  DirectionsCar,
  Person,
  Warning,
  CheckCircle,
  Cancel,
  Edit,
  Visibility,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useThemeStore } from '../../store/themeStore';
import { WebSocketService } from '../../services/WebSocketService';

const LiveOperations = () => {
  const { isDarkMode } = useThemeStore();
  const [liveData, setLiveData] = useState({
    activeTrips: [],
    availableDrivers: [],
    systemAlerts: [],
    realTimeMetrics: {
      activeVehicles: 0,
      ongoingTrips: 0,
      availableDrivers: 0,
      pendingRequests: 0,
    },
  });
  const [selectedTrip, setSelectedTrip] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Initialize live data
    loadLiveData();

    // Subscribe to real-time updates
    const unsubscribe = WebSocketService.subscribe('/topic/live-operations', (data) => {
      setLiveData(prevData => ({
        ...prevData,
        ...data,
      }));
    });

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, []);

  const loadLiveData = async () => {
    setRefreshing(true);
    try {
      // Simulate API call with mock data
      setTimeout(() => {
        setLiveData({
          activeTrips: generateMockTrips(),
          availableDrivers: generateMockDrivers(),
          systemAlerts: generateMockAlerts(),
          realTimeMetrics: {
            activeVehicles: Math.floor(Math.random() * 50) + 30,
            ongoingTrips: Math.floor(Math.random() * 25) + 15,
            availableDrivers: Math.floor(Math.random() * 20) + 10,
            pendingRequests: Math.floor(Math.random() * 10) + 2,
          },
        });
        setRefreshing(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to load live data:', error);
      setRefreshing(false);
    }
  };

  const generateMockTrips = () => {
    return Array.from({ length: 15 }, (_, i) => ({
      id: `TRIP-${String(i + 1).padStart(4, '0')}`,
      driver: `Driver ${Math.floor(Math.random() * 50) + 1}`,
      passenger: `Passenger ${Math.floor(Math.random() * 100) + 1}`,
      status: ['ongoing', 'pickup', 'dropoff'][Math.floor(Math.random() * 3)],
      startLocation: 'Downtown Area',
      endLocation: 'Airport Terminal',
      estimatedTime: `${Math.floor(Math.random() * 30) + 5} min`,
      fare: `$${(Math.random() * 50 + 10).toFixed(2)}`,
      startTime: new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString(),
    }));
  };

  const generateMockDrivers = () => {
    return Array.from({ length: 12 }, (_, i) => ({
      id: i + 1,
      name: `Driver ${i + 1}`,
      vehicle: `VH-${String(i + 1).padStart(3, '0')}`,
      location: `Zone ${Math.floor(Math.random() * 10) + 1}`,
      status: 'available',
      rating: (Math.random() * 2 + 3).toFixed(1),
      completedTrips: Math.floor(Math.random() * 20) + 5,
    }));
  };

  const generateMockAlerts = () => {
    return [
      {
        id: 1,
        type: 'warning',
        title: 'High Demand Area',
        message: 'Increased ride requests in Downtown area',
        timestamp: new Date().toLocaleTimeString(),
      },
      {
        id: 2,
        type: 'info',
        title: 'Driver Break',
        message: 'Driver #25 has started break period',
        timestamp: new Date().toLocaleTimeString(),
      },
      {
        id: 3,
        type: 'error',
        title: 'Vehicle Issue',
        message: 'Vehicle VH-012 reported mechanical issue',
        timestamp: new Date().toLocaleTimeString(),
      },
    ];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ongoing': return 'primary';
      case 'pickup': return 'warning';
      case 'dropoff': return 'success';
      default: return 'default';
    }
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Cancel color="error" />;
      case 'info': return <CheckCircle color="info" />;
      default: return <CheckCircle />;
    }
  };

  const handleTripClick = (trip) => {
    setSelectedTrip(trip);
    setDialogOpen(true);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Live Operations Center
        </Typography>
        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={loadLiveData}
          disabled={refreshing}
        >
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </Box>

      {/* Real-time Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Active Vehicles', value: liveData.realTimeMetrics.activeVehicles, icon: <DirectionsCar />, color: 'primary' },
          { title: 'Ongoing Trips', value: liveData.realTimeMetrics.ongoingTrips, icon: <LocationOn />, color: 'success' },
          { title: 'Available Drivers', value: liveData.realTimeMetrics.availableDrivers, icon: <Person />, color: 'info' },
          { title: 'Pending Requests', value: liveData.realTimeMetrics.pendingRequests, icon: <Warning />, color: 'warning' },
        ].map((metric, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Typography variant="h4" fontWeight="bold">
                        {metric.value}
                      </Typography>
                    </Box>
                    <Box sx={{ color: `${metric.color}.main` }}>
                      {metric.icon}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Active Trips */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Trips
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Trip ID</TableCell>
                      <TableCell>Driver</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Route</TableCell>
                      <TableCell>ETA</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {liveData.activeTrips.slice(0, 8).map((trip) => (
                      <TableRow key={trip.id} hover>
                        <TableCell>{trip.id}</TableCell>
                        <TableCell>{trip.driver}</TableCell>
                        <TableCell>
                          <Chip
                            label={trip.status}
                            color={getStatusColor(trip.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {trip.startLocation} → {trip.endLocation}
                        </TableCell>
                        <TableCell>{trip.estimatedTime}</TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => handleTripClick(trip)}
                          >
                            <Visibility />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* System Alerts & Available Drivers */}
        <Grid item xs={12} lg={4}>
          <Grid container spacing={2}>
            {/* System Alerts */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Alerts
                  </Typography>
                  {liveData.systemAlerts.map((alert) => (
                    <Box
                      key={alert.id}
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        mb: 2,
                        p: 2,
                        borderRadius: 1,
                        bgcolor: isDarkMode ? 'grey.800' : 'grey.50',
                      }}
                    >
                      <Box sx={{ mr: 2, mt: 0.5 }}>
                        {getAlertIcon(alert.type)}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {alert.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {alert.timestamp}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            {/* Available Drivers */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Available Drivers ({liveData.availableDrivers.length})
                  </Typography>
                  {liveData.availableDrivers.slice(0, 6).map((driver) => (
                    <Box
                      key={driver.id}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        py: 1,
                        borderBottom: '1px solid',
                        borderColor: 'divider',
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle2">
                          {driver.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {driver.vehicle} • {driver.location}
                        </Typography>
                      </Box>
                      <Chip
                        label={`★ ${driver.rating}`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Trip Details Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Trip Details - {selectedTrip?.id}</DialogTitle>
        <DialogContent>
          {selectedTrip && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Driver"
                  value={selectedTrip.driver}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Passenger"
                  value={selectedTrip.passenger}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Location"
                  value={selectedTrip.startLocation}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Location"
                  value={selectedTrip.endLocation}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Status"
                  value={selectedTrip.status}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Fare"
                  value={selectedTrip.fare}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
          <Button variant="contained" startIcon={<Edit />}>
            Edit Trip
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LiveOperations;
