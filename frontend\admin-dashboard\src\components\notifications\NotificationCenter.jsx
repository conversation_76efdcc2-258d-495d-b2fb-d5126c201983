import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Divider,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  Notifications,
  NotificationsActive,
  Warning,
  Error,
  Info,
  CheckCircle,
  Close,
  Settings,
  VolumeUp,
  VolumeOff,
  Email,
  Sms,
  Slack,
  Download,
  Clear,
  FilterList,
  Search,
  Security,
  Speed,
  DirectionsCar,
  Person,
  AttachMoney,
  Schedule,
  Build,
  LocalShipping,
  Traffic,
  BugReport,
  SystemUpdate,
  NetworkCheck,
  Storage,
  Memory,
  Computer,
  CloudSync,
  DataUsage,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';
import { WebSocketService } from '../../services/WebSocketService';

const NotificationCenter = () => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [tabValue, setTabValue] = useState(0);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [slackEnabled, setSlackEnabled] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Priority levels with colors and icons
  const priorityConfig = {
    P1: {
      label: 'Critical',
      color: theme.palette.error.main,
      icon: <Error />,
      sound: 'critical-alert.mp3',
      autoEscalate: true,
      escalateAfter: 300000, // 5 minutes
    },
    P2: { 
      label: 'High', 
      color: theme.palette.warning.main, 
      icon: <Warning />,
      sound: 'high-alert.mp3',
      autoEscalate: true,
      escalateAfter: 900000, // 15 minutes
    },
    P3: { 
      label: 'Medium', 
      color: theme.palette.info.main, 
      icon: <Info />,
      sound: 'medium-alert.mp3',
      autoEscalate: false,
    },
    P4: { 
      label: 'Low', 
      color: theme.palette.success.main, 
      icon: <CheckCircle />,
      sound: null,
      autoEscalate: false,
    },
  };

  // Notification categories
  const categories = {
    system: { label: 'System', icon: <Computer />, color: theme.palette.primary.main },
    fleet: { label: 'Fleet', icon: <DirectionsCar />, color: theme.palette.secondary.main },
    trips: { label: 'Trips', icon: <LocalShipping />, color: theme.palette.info.main },
    users: { label: 'Users', icon: <Person />, color: theme.palette.warning.main },
    finance: { label: 'Finance', icon: <AttachMoney />, color: theme.palette.success.main },
    security: { label: 'Security', icon: <Security />, color: theme.palette.error.main },
    maintenance: { label: 'Maintenance', icon: <Build />, color: theme.palette.grey[600] },
    performance: { label: 'Performance', icon: <Speed />, color: theme.palette.purple?.main || theme.palette.primary.main },
  };

  useEffect(() => {
    // Load initial notifications
    loadNotifications();

    // Subscribe to real-time notifications only if WebSocketService is available
    let unsubscribe1 = null;
    let unsubscribe2 = null;

    try {
      if (WebSocketService && typeof WebSocketService.subscribe === 'function') {
        unsubscribe1 = WebSocketService.subscribe('/topic/notifications', handleNewNotification);
        unsubscribe2 = WebSocketService.subscribe('/topic/alerts', handleSystemAlert);
      }
    } catch (error) {
      console.warn('WebSocket subscription failed:', error);
    }

    return () => {
      try {
        if (unsubscribe1 && typeof unsubscribe1 === 'function') {
          unsubscribe1();
        }
        if (unsubscribe2 && typeof unsubscribe2 === 'function') {
          unsubscribe2();
        }
      } catch (error) {
        console.warn('Error during WebSocket cleanup:', error);
      }
    };
  }, []);

  const loadNotifications = useCallback(() => {
    // Mock notifications with various priorities and categories
    const mockNotifications = [
      {
        id: 'notif-001',
        title: 'Critical System Failure',
        message: 'Database connection lost. Immediate attention required.',
        priority: 'P1',
        category: 'system',
        timestamp: new Date(Date.now() - 300000),
        read: false,
        acknowledged: false,
        escalated: false,
        source: 'Database Monitor',
        actions: ['restart-service', 'check-logs', 'escalate'],
      },
      {
        id: 'notif-002',
        title: 'Vehicle Maintenance Overdue',
        message: 'Vehicle VH-001 has exceeded maintenance schedule by 500km.',
        priority: 'P2',
        category: 'fleet',
        timestamp: new Date(Date.now() - 600000),
        read: false,
        acknowledged: true,
        escalated: false,
        source: 'Fleet Management',
        actions: ['schedule-maintenance', 'disable-vehicle'],
      },
      {
        id: 'notif-003',
        title: 'High Trip Demand',
        message: 'Trip requests increased by 150% in downtown area.',
        priority: 'P3',
        category: 'trips',
        timestamp: new Date(Date.now() - 900000),
        read: true,
        acknowledged: true,
        escalated: false,
        source: 'Trip Analytics',
        actions: ['deploy-more-vehicles', 'adjust-pricing'],
      },
      {
        id: 'notif-004',
        title: 'New Driver Registration',
        message: '5 new drivers completed registration and verification.',
        priority: 'P4',
        category: 'users',
        timestamp: new Date(Date.now() - 1200000),
        read: true,
        acknowledged: true,
        escalated: false,
        source: 'User Management',
        actions: ['review-applications'],
      },
      {
        id: 'notif-005',
        title: 'Payment Gateway Error',
        message: 'Multiple payment failures detected. Revenue impact: $2,450.',
        priority: 'P1',
        category: 'finance',
        timestamp: new Date(Date.now() - 180000),
        read: false,
        acknowledged: false,
        escalated: true,
        source: 'Payment Processor',
        actions: ['check-gateway', 'contact-provider', 'enable-backup'],
      },
      {
        id: 'notif-006',
        title: 'Security Alert',
        message: 'Suspicious login attempts detected from unknown IP addresses.',
        priority: 'P2',
        category: 'security',
        timestamp: new Date(Date.now() - 450000),
        read: false,
        acknowledged: false,
        escalated: false,
        source: 'Security Monitor',
        actions: ['block-ips', 'review-logs', 'notify-users'],
      },
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.read).length);
  }, []);

  const handleNewNotification = useCallback((notification) => {
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Play sound if enabled
    if (soundEnabled && priorityConfig[notification.priority]?.sound) {
      playNotificationSound(priorityConfig[notification.priority].sound);
    }

    // Show snackbar for high priority notifications
    if (['P1', 'P2'].includes(notification.priority)) {
      setSnackbarMessage(`${priorityConfig[notification.priority].label}: ${notification.title}`);
      setSnackbarOpen(true);
    }

    // Auto-escalate if configured
    if (priorityConfig[notification.priority]?.autoEscalate) {
      setTimeout(() => {
        escalateNotification(notification.id);
      }, priorityConfig[notification.priority].escalateAfter);
    }
  }, [soundEnabled]);

  const handleSystemAlert = useCallback((alert) => {
    // Convert system alert to notification
    const notification = {
      id: `alert-${Date.now()}`,
      title: alert.title || 'System Alert',
      message: alert.message,
      priority: alert.severity === 'critical' ? 'P1' : alert.severity === 'warning' ? 'P2' : 'P3',
      category: 'system',
      timestamp: new Date(),
      read: false,
      acknowledged: false,
      escalated: false,
      source: alert.source || 'System Monitor',
      actions: alert.actions || ['acknowledge'],
    };

    handleNewNotification(notification);
  }, [handleNewNotification]);

  const playNotificationSound = (soundFile) => {
    try {
      const audio = new Audio(`/sounds/${soundFile}`);
      audio.volume = 0.7;
      audio.play().catch(console.error);
    } catch (error) {
      console.error('Error playing notification sound:', error);
    }
  };

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const acknowledgeNotification = (notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, acknowledged: true, read: true } : n
      )
    );
  };

  const escalateNotification = (notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, escalated: true } : n
      )
    );

    // Send to Slack/Email if enabled
    if (slackEnabled || emailEnabled) {
      sendEscalationAlert(notificationId);
    }
  };

  const sendEscalationAlert = async (notificationId) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return;

    try {
      // Mock API call to send escalation
      console.log('Sending escalation alert:', notification);
      
      if (slackEnabled) {
        // Send to Slack
        await fetch('/api/notifications/slack', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            channel: '#alerts',
            text: `🚨 ESCALATED: ${notification.title}`,
            attachments: [{
              color: priorityConfig[notification.priority].color,
              fields: [
                { title: 'Priority', value: notification.priority, short: true },
                { title: 'Category', value: notification.category, short: true },
                { title: 'Message', value: notification.message, short: false },
              ],
            }],
          }),
        });
      }

      if (emailEnabled) {
        // Send email alert
        await fetch('/api/notifications/email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            to: '<EMAIL>',
            subject: `ESCALATED ALERT: ${notification.title}`,
            body: `Priority: ${notification.priority}\nCategory: ${notification.category}\nMessage: ${notification.message}\nTime: ${notification.timestamp}`,
          }),
        });
      }
    } catch (error) {
      console.error('Error sending escalation alert:', error);
    }
  };

  const clearAllNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const exportNotifications = () => {
    const csvContent = notifications.map(n => 
      `${n.timestamp.toISOString()},${n.priority},${n.category},${n.title},"${n.message}",${n.read},${n.acknowledged}`
    ).join('\n');
    
    const header = 'Timestamp,Priority,Category,Title,Message,Read,Acknowledged\n';
    const blob = new Blob([header + csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notifications-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesPriority = selectedPriority === 'all' || notification.priority === selectedPriority;
    const matchesCategory = selectedCategory === 'all' || notification.category === selectedCategory;
    const matchesSearch = !searchTerm || 
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    switch (tabValue) {
      case 0: return matchesPriority && matchesCategory && matchesSearch; // All
      case 1: return !notification.read && matchesPriority && matchesCategory && matchesSearch; // Unread
      case 2: return ['P1', 'P2'].includes(notification.priority) && matchesPriority && matchesCategory && matchesSearch; // Critical
      case 3: return notification.escalated && matchesPriority && matchesCategory && matchesSearch; // Escalated
      default: return false;
    }
  });

  const getNotificationIcon = (notification) => {
    if (notification.escalated) return <Error color="error" />;
    return priorityConfig[notification.priority]?.icon || <Info />;
  };

  const getNotificationColor = (notification) => {
    if (notification.escalated) return theme.palette.error.main;
    return priorityConfig[notification.priority]?.color || theme.palette.info.main;
  };

  return (
    <>
      {/* Notification Bell */}
      <Tooltip title="Notifications">
        <IconButton
          onClick={(e) => setAnchorEl(e.currentTarget)}
          sx={{ 
            position: 'relative',
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          }}
        >
          <Badge badgeContent={unreadCount} color="error" max={99}>
            {unreadCount > 0 ? (
              <NotificationsActive color="primary" />
            ) : (
              <Notifications />
            )}
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Notification Panel */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        PaperProps={{
          sx: {
            width: 450,
            maxHeight: 600,
            overflow: 'hidden',
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" fontWeight="bold">
              Notifications
            </Typography>
            <Box>
              <IconButton size="small" onClick={() => setFilterOpen(true)}>
                <FilterList />
              </IconButton>
              <IconButton size="small" onClick={() => setSettingsOpen(true)}>
                <Settings />
              </IconButton>
              <IconButton size="small" onClick={exportNotifications}>
                <Download />
              </IconButton>
              <IconButton size="small" onClick={clearAllNotifications}>
                <Clear />
              </IconButton>
            </Box>
          </Box>

          <Tabs
            value={tabValue}
            onChange={(e, newValue) => setTabValue(newValue)}
            variant="fullWidth"
            sx={{ minHeight: 36 }}
          >
            <Tab label={`All (${notifications.length})`} sx={{ minHeight: 36, fontSize: '0.75rem' }} />
            <Tab label={`Unread (${unreadCount})`} sx={{ minHeight: 36, fontSize: '0.75rem' }} />
            <Tab label="Critical" sx={{ minHeight: 36, fontSize: '0.75rem' }} />
            <Tab label="Escalated" sx={{ minHeight: 36, fontSize: '0.75rem' }} />
          </Tabs>
        </Box>

        <List sx={{ maxHeight: 400, overflow: 'auto', p: 0 }}>
          <AnimatePresence>
            {filteredNotifications.length === 0 ? (
              <ListItem>
                <ListItemText
                  primary="No notifications"
                  secondary="You're all caught up!"
                  sx={{ textAlign: 'center' }}
                />
              </ListItem>
            ) : (
              filteredNotifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <ListItem
                    sx={{
                      borderLeft: 4,
                      borderLeftColor: getNotificationColor(notification),
                      backgroundColor: !notification.read ? theme.palette.action.hover : 'transparent',
                      '&:hover': {
                        backgroundColor: theme.palette.action.selected,
                      },
                    }}
                    onClick={() => markAsRead(notification.id)}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getNotificationColor(notification), width: 32, height: 32 }}>
                        {getNotificationIcon(notification)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography variant="subtitle2" fontWeight={!notification.read ? 'bold' : 'normal'}>
                            {notification.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Chip
                              label={notification.priority}
                              size="small"
                              sx={{
                                bgcolor: getNotificationColor(notification),
                                color: 'white',
                                fontSize: '0.6rem',
                                height: 16,
                              }}
                            />
                            {notification.escalated && (
                              <Chip
                                label="ESC"
                                size="small"
                                color="error"
                                sx={{ fontSize: '0.6rem', height: 16 }}
                              />
                            )}
                          </Box>
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                            {notification.message}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="caption" color="text.secondary">
                              {notification.timestamp.toLocaleTimeString()} • {notification.source}
                            </Typography>
                            {!notification.acknowledged && (
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  acknowledgeNotification(notification.id);
                                }}
                                sx={{ fontSize: '0.6rem', py: 0.25 }}
                              >
                                ACK
                              </Button>
                            )}
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < filteredNotifications.length - 1 && <Divider />}
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </List>

        {filteredNotifications.length > 0 && (
          <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider', textAlign: 'center' }}>
            <Button size="small" onClick={() => setAnchorEl(null)}>
              View All in Dashboard
            </Button>
          </Box>
        )}
      </Menu>

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Notification Settings</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={soundEnabled}
                  onChange={(e) => setSoundEnabled(e.target.checked)}
                />
              }
              label="Sound Notifications"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={emailEnabled}
                  onChange={(e) => setEmailEnabled(e.target.checked)}
                />
              }
              label="Email Notifications"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={slackEnabled}
                  onChange={(e) => setSlackEnabled(e.target.checked)}
                />
              }
              label="Slack Integration"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setSettingsOpen(false)}>
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>

      {/* Critical Alert Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity="error"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>

      {/* Emergency FAB for Critical Alerts */}
      {notifications.some(n => n.priority === 'P1' && !n.acknowledged) && (
        <Fab
          color="error"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.1)' },
              '100%': { transform: 'scale(1)' },
            },
          }}
          onClick={() => setAnchorEl(document.body)}
        >
          <Error />
        </Fab>
      )}
    </>
  );
};

export default NotificationCenter;
