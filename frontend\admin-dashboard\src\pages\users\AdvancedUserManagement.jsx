import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Checkbox,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  Snackbar,
  Tooltip,
  Badge,
  LinearProgress,
  CircularProgress,
  Menu,
  Pagination,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  FilterList,
  Person,
  DriveEta,
  AdminPanelSettings,
  Block,
  CheckCircle,
  Download,
  Upload,
  Visibility,
  VisibilityOff,
  Security,
  History,
  Star,
  Phone,
  Email,
  LocationOn,
  Schedule,
  TrendingUp,
  Warning,
  Error,
  Info,
  ExpandMore,
  MoreVert,
  Refresh,
  ImportExport,
  Assignment,
  VerifiedUser,
  PersonAdd,
  GroupAdd,
  Settings,
  Analytics,
  Timeline,
  Assessment,
  AccountBox,
  ContactPhone,
  Badge as BadgeIcon,
  Work,
  School,
  Home,
  Business,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';

const AdvancedUserManagement = () => {
  const theme = useTheme();
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [bulkActionDialog, setBulkActionDialog] = useState(false);
  const [importDialog, setImportDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterVerification, setFilterVerification] = useState('all');
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(25);
  const [sortBy, setSortBy] = useState('joinDate');
  const [sortOrder, setSortOrder] = useState('desc');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedUserForMenu, setSelectedUserForMenu] = useState(null);

  // User roles with permissions
  const userRoles = {
    passenger: {
      label: 'Passenger',
      icon: <Person />,
      color: theme.palette.primary.main,
      permissions: ['book_trip', 'view_history', 'rate_driver', 'contact_support'],
    },
    driver: {
      label: 'Driver',
      icon: <DriveEta />,
      color: theme.palette.warning.main,
      permissions: ['accept_trips', 'update_location', 'manage_vehicle', 'view_earnings', 'contact_support'],
    },
    operator: {
      label: 'Operator',
      icon: <Work />,
      color: theme.palette.info.main,
      permissions: ['manage_trips', 'assign_drivers', 'view_analytics', 'contact_users'],
    },
    admin: {
      label: 'Admin',
      icon: <AdminPanelSettings />,
      color: theme.palette.error.main,
      permissions: ['full_access', 'manage_users', 'system_settings', 'view_reports'],
    },
    support: {
      label: 'Support',
      icon: <ContactPhone />,
      color: theme.palette.success.main,
      permissions: ['view_users', 'handle_complaints', 'access_chat', 'generate_reports'],
    },
  };

  // Verification statuses
  const verificationStatuses = {
    pending: { label: 'Pending', color: 'warning', icon: <Schedule /> },
    verified: { label: 'Verified', color: 'success', icon: <VerifiedUser /> },
    rejected: { label: 'Rejected', color: 'error', icon: <Error /> },
    incomplete: { label: 'Incomplete', color: 'info', icon: <Info /> },
  };

  useEffect(() => {
    loadUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, filterRole, filterStatus, filterVerification, tabValue, sortBy, sortOrder]);

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockUsers = generateMockUsers(250);
      setUsers(mockUsers);
    } catch (error) {
      showSnackbar('Error loading users', 'error');
    } finally {
      setLoading(false);
    }
  }, []);

  const generateMockUsers = (count) => {
    const roles = Object.keys(userRoles);
    const statuses = ['active', 'inactive', 'suspended', 'banned'];
    const verifications = Object.keys(verificationStatuses);
    const cities = ['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina', 'Khobar', 'Tabuk', 'Abha'];
    
    return Array.from({ length: count }, (_, i) => {
      const role = roles[Math.floor(Math.random() * roles.length)];
      const joinDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
      const lastActive = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      
      return {
        id: `user-${String(i + 1).padStart(4, '0')}`,
        name: `User ${i + 1}`,
        email: `user${i + 1}@tecnodrive.com`,
        phone: `+966 5${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        role,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        verification: verifications[Math.floor(Math.random() * verifications.length)],
        joinDate,
        lastActive,
        city: cities[Math.floor(Math.random() * cities.length)],
        totalTrips: role === 'passenger' ? Math.floor(Math.random() * 100) + 1 : 
                   role === 'driver' ? Math.floor(Math.random() * 500) + 10 : 0,
        rating: role !== 'admin' && role !== 'support' ? (Math.random() * 2 + 3).toFixed(1) : null,
        earnings: role === 'driver' ? Math.floor(Math.random() * 10000) + 1000 : null,
        documents: {
          identity: Math.random() > 0.2,
          license: role === 'driver' ? Math.random() > 0.1 : null,
          vehicle: role === 'driver' ? Math.random() > 0.15 : null,
          insurance: role === 'driver' ? Math.random() > 0.2 : null,
        },
        flags: {
          vip: Math.random() > 0.95,
          blacklisted: Math.random() > 0.98,
          newUser: (Date.now() - joinDate.getTime()) < 7 * 24 * 60 * 60 * 1000,
          highValue: role === 'passenger' && Math.random() > 0.9,
        },
        metrics: {
          completionRate: role === 'driver' ? (Math.random() * 20 + 80).toFixed(1) : null,
          responseTime: role === 'driver' ? Math.floor(Math.random() * 300) + 30 : null,
          cancellationRate: (Math.random() * 10).toFixed(1),
          complaintCount: Math.floor(Math.random() * 5),
        },
      };
    });
  };

  const filterUsers = useCallback(() => {
    let filtered = [...users];

    // Filter by tab (role groups)
    if (tabValue === 1) filtered = filtered.filter(user => user.role === 'passenger');
    if (tabValue === 2) filtered = filtered.filter(user => user.role === 'driver');
    if (tabValue === 3) filtered = filtered.filter(user => ['admin', 'operator', 'support'].includes(user.role));
    if (tabValue === 4) filtered = filtered.filter(user => user.flags.vip || user.flags.highValue);
    if (tabValue === 5) filtered = filtered.filter(user => user.status === 'suspended' || user.flags.blacklisted);

    // Apply filters
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.phone.includes(term) ||
        user.id.toLowerCase().includes(term)
      );
    }

    if (filterRole !== 'all') {
      filtered = filtered.filter(user => user.role === filterRole);
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(user => user.status === filterStatus);
    }

    if (filterVerification !== 'all') {
      filtered = filtered.filter(user => user.verification === filterVerification);
    }

    // Sort users
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'joinDate' || sortBy === 'lastActive') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredUsers(filtered);
  }, [users, searchTerm, filterRole, filterStatus, filterVerification, tabValue, sortBy, sortOrder]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleUserAction = (action, userId) => {
    switch (action) {
      case 'suspend':
        setUsers(prev => prev.map(user => 
          user.id === userId ? { ...user, status: 'suspended' } : user
        ));
        showSnackbar('User suspended successfully', 'warning');
        break;
      case 'activate':
        setUsers(prev => prev.map(user => 
          user.id === userId ? { ...user, status: 'active' } : user
        ));
        showSnackbar('User activated successfully', 'success');
        break;
      case 'verify':
        setUsers(prev => prev.map(user => 
          user.id === userId ? { ...user, verification: 'verified' } : user
        ));
        showSnackbar('User verified successfully', 'success');
        break;
      case 'ban':
        setUsers(prev => prev.map(user => 
          user.id === userId ? { ...user, status: 'banned' } : user
        ));
        showSnackbar('User banned successfully', 'error');
        break;
      default:
        break;
    }
  };

  const handleBulkAction = (action) => {
    if (selectedUsers.length === 0) {
      showSnackbar('Please select users first', 'warning');
      return;
    }

    switch (action) {
      case 'suspend':
        setUsers(prev => prev.map(user => 
          selectedUsers.includes(user.id) ? { ...user, status: 'suspended' } : user
        ));
        showSnackbar(`${selectedUsers.length} users suspended`, 'warning');
        break;
      case 'activate':
        setUsers(prev => prev.map(user => 
          selectedUsers.includes(user.id) ? { ...user, status: 'active' } : user
        ));
        showSnackbar(`${selectedUsers.length} users activated`, 'success');
        break;
      case 'export':
        exportUsers(selectedUsers);
        break;
      default:
        break;
    }

    setSelectedUsers([]);
    setBulkActionDialog(false);
  };

  const exportUsers = (userIds = null) => {
    const usersToExport = userIds ? users.filter(u => userIds.includes(u.id)) : filteredUsers;
    
    const csvContent = usersToExport.map(user => 
      `${user.id},${user.name},${user.email},${user.phone},${user.role},${user.status},${user.verification},${user.joinDate.toISOString()},${user.lastActive.toISOString()},${user.totalTrips || 0},${user.rating || ''}`
    ).join('\n');
    
    const header = 'ID,Name,Email,Phone,Role,Status,Verification,Join Date,Last Active,Total Trips,Rating\n';
    const blob = new Blob([header + csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
    
    showSnackbar('Users exported successfully', 'success');
  };

  const getUserStats = () => {
    const total = users.length;
    const active = users.filter(u => u.status === 'active').length;
    const passengers = users.filter(u => u.role === 'passenger').length;
    const drivers = users.filter(u => u.role === 'driver').length;
    const verified = users.filter(u => u.verification === 'verified').length;
    const newUsers = users.filter(u => u.flags.newUser).length;

    return { total, active, passengers, drivers, verified, newUsers };
  };

  const stats = getUserStats();
  const paginatedUsers = filteredUsers.slice((page - 1) * rowsPerPage, page * rowsPerPage);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Advanced User Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<ImportExport />}
            onClick={() => setImportDialog(true)}
          >
            Import/Export
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadUsers}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={() => {
              setSelectedUser(null);
              setDialogOpen(true);
            }}
          >
            Add User
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Users', value: stats.total, icon: <Person />, color: 'primary' },
          { title: 'Active Users', value: stats.active, icon: <CheckCircle />, color: 'success' },
          { title: 'Passengers', value: stats.passengers, icon: <Person />, color: 'info' },
          { title: 'Drivers', value: stats.drivers, icon: <DriveEta />, color: 'warning' },
          { title: 'Verified', value: stats.verified, icon: <VerifiedUser />, color: 'success' },
          { title: 'New Users (7d)', value: stats.newUsers, icon: <PersonAdd />, color: 'secondary' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={2} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ color: `${stat.color}.main`, mb: 1 }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h5" fontWeight="bold">
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                  label="Role"
                >
                  <MenuItem value="all">All Roles</MenuItem>
                  {Object.entries(userRoles).map(([key, role]) => (
                    <MenuItem key={key} value={key}>{role.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                  <MenuItem value="banned">Banned</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Verification</InputLabel>
                <Select
                  value={filterVerification}
                  onChange={(e) => setFilterVerification(e.target.value)}
                  label="Verification"
                >
                  <MenuItem value="all">All</MenuItem>
                  {Object.entries(verificationStatuses).map(([key, status]) => (
                    <MenuItem key={key} value={key}>{status.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={() => exportUsers()}
                  fullWidth
                >
                  Export
                </Button>
                {selectedUsers.length > 0 && (
                  <Button
                    variant="contained"
                    color="warning"
                    onClick={() => setBulkActionDialog(true)}
                  >
                    Bulk ({selectedUsers.length})
                  </Button>
                )}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* User Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label={`All Users (${stats.total})`} />
            <Tab label={`Passengers (${stats.passengers})`} />
            <Tab label={`Drivers (${stats.drivers})`} />
            <Tab label="Staff" />
            <Tab label="VIP/High Value" />
            <Tab label="Flagged" />
          </Tabs>
        </Box>

        <CardContent>
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedUsers.length > 0 && selectedUsers.length < paginatedUsers.length}
                      checked={paginatedUsers.length > 0 && selectedUsers.length === paginatedUsers.length}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers(paginatedUsers.map(u => u.id));
                        } else {
                          setSelectedUsers([]);
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Verification</TableCell>
                  <TableCell>Activity</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedUsers(prev => [...prev, user.id]);
                          } else {
                            setSelectedUsers(prev => prev.filter(id => id !== user.id));
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Badge
                          overlap="circular"
                          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                          badgeContent={
                            user.flags.vip ? <Star sx={{ fontSize: 12, color: 'gold' }} /> :
                            user.flags.newUser ? <PersonAdd sx={{ fontSize: 12, color: 'green' }} /> :
                            user.flags.blacklisted ? <Block sx={{ fontSize: 12, color: 'red' }} /> : null
                          }
                        >
                          <Avatar sx={{ mr: 2, bgcolor: userRoles[user.role]?.color }}>
                            {userRoles[user.role]?.icon}
                          </Avatar>
                        </Badge>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {user.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {user.email}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.phone} • {user.city}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={userRoles[user.role]?.label}
                        size="small"
                        sx={{ bgcolor: userRoles[user.role]?.color, color: 'white' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={user.status}
                        size="small"
                        color={
                          user.status === 'active' ? 'success' :
                          user.status === 'suspended' ? 'warning' :
                          user.status === 'banned' ? 'error' : 'default'
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={verificationStatuses[user.verification]?.label}
                        size="small"
                        color={verificationStatuses[user.verification]?.color}
                        icon={verificationStatuses[user.verification]?.icon}
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          Joined: {user.joinDate.toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Last: {user.lastActive.toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Trips: {user.totalTrips || 0}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {user.rating && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Star sx={{ fontSize: 16, color: 'gold', mr: 0.5 }} />
                          <Typography variant="body2">{user.rating}</Typography>
                        </Box>
                      )}
                      {user.role === 'driver' && user.metrics.completionRate && (
                        <Typography variant="caption" color="text.secondary">
                          {user.metrics.completionRate}% completion
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          setMenuAnchor(e.currentTarget);
                          setSelectedUserForMenu(user);
                        }}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={Math.ceil(filteredUsers.length / rowsPerPage)}
              page={page}
              onChange={(e, newPage) => setPage(newPage)}
              color="primary"
            />
          </Box>
        </CardContent>
      </Card>

      {/* User Actions Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          setSelectedUser(selectedUserForMenu);
          setDialogOpen(true);
          setMenuAnchor(null);
        }}>
          <Edit sx={{ mr: 1 }} /> Edit User
        </MenuItem>
        <MenuItem onClick={() => {
          handleUserAction('verify', selectedUserForMenu?.id);
          setMenuAnchor(null);
        }}>
          <VerifiedUser sx={{ mr: 1 }} /> Verify
        </MenuItem>
        <MenuItem onClick={() => {
          handleUserAction(selectedUserForMenu?.status === 'active' ? 'suspend' : 'activate', selectedUserForMenu?.id);
          setMenuAnchor(null);
        }}>
          {selectedUserForMenu?.status === 'active' ? <Block sx={{ mr: 1 }} /> : <CheckCircle sx={{ mr: 1 }} />}
          {selectedUserForMenu?.status === 'active' ? 'Suspend' : 'Activate'}
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          handleUserAction('ban', selectedUserForMenu?.id);
          setMenuAnchor(null);
        }} sx={{ color: 'error.main' }}>
          <Block sx={{ mr: 1 }} /> Ban User
        </MenuItem>
      </Menu>

      {/* Bulk Actions Dialog */}
      <Dialog open={bulkActionDialog} onClose={() => setBulkActionDialog(false)}>
        <DialogTitle>Bulk Actions ({selectedUsers.length} users)</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Select an action to apply to {selectedUsers.length} selected users:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
            <Button onClick={() => handleBulkAction('activate')} startIcon={<CheckCircle />}>
              Activate Users
            </Button>
            <Button onClick={() => handleBulkAction('suspend')} startIcon={<Block />} color="warning">
              Suspend Users
            </Button>
            <Button onClick={() => handleBulkAction('export')} startIcon={<Download />}>
              Export Selected
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkActionDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdvancedUserManagement;
