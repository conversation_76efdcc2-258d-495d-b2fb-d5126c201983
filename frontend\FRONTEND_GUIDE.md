# 🖥️ TECNO DRIVE Frontend Applications Guide

## 📋 Overview
TECNO DRIVE platform includes 5 comprehensive frontend applications designed for different user roles and use cases.

## 🎯 Applications

### 1. 🏢 Admin Dashboard
**Technology:** React 18 + TypeScript + Material-UI
**Port:** 3000
**URL:** http://localhost:3000

**Features:**
- 📊 Real-time analytics and reporting
- 🗺️ Interactive maps with Leaflet
- 📈 Advanced charts with Chart.js and D3.js
- 🔄 Live data updates with WebSocket
- 📱 Responsive design
- 🎛️ Drag & drop dashboard customization

**Key Libraries:**
- Material-UI for UI components
- React Query for data management
- Zustand for state management
- Leaflet for mapping
- Chart.js & D3.js for visualizations

### 2. 🚨 Live Operations Dashboard
**Technology:** React 18 + Ant Design
**Port:** 3001
**URL:** http://localhost:3001

**Features:**
- 🔴 Real-time operations monitoring
- 🚗 Live vehicle tracking
- 📡 WebSocket connections for instant updates
- 🗺️ Interactive fleet maps
- 📊 Operational metrics
- 🚨 Alert management

**Key Libraries:**
- Ant Design for UI components
- STOMP.js for WebSocket communication
- React Leaflet for mapping
- Chart.js for real-time charts

### 3. 👨‍💼 Operator Dashboard
**Technology:** Angular 20 + TypeScript
**Port:** 4200
**URL:** http://localhost:4200

**Features:**
- 🎛️ Fleet management interface
- 📋 Driver management
- 📊 Performance analytics
- 🔧 System configuration
- 📱 Responsive Angular Material design

**Key Libraries:**
- Angular 20 (Latest version)
- Angular Material for UI
- RxJS for reactive programming
- Server-Side Rendering (SSR)

### 4. 🚗 Driver Mobile App
**Technology:** React Native + Expo
**Port:** 19006 (Web mode)
**URL:** http://localhost:19006

**Features:**
- 📱 Native mobile experience
- 🗺️ GPS navigation and tracking
- 📷 Camera integration
- 🔔 Push notifications
- 📍 Real-time location updates
- 💰 Earnings tracking

**Key Libraries:**
- Expo SDK 49
- React Navigation 6
- React Native Maps
- Expo Location & Camera
- React Hook Form

### 5. 👥 Passenger Mobile App
**Technology:** React Native + Expo
**Port:** 19007 (Web mode)
**URL:** http://localhost:19007

**Features:**
- 🚖 Ride booking interface
- 🗺️ Route planning and tracking
- 💳 Payment integration
- ⭐ Rating and feedback system
- 📱 Native mobile experience
- 🔔 Real-time notifications

**Key Libraries:**
- Expo SDK 49
- React Navigation 6
- React Native Maps
- Expo Location
- React Query for API calls

## 🚀 Quick Start

### Start All Applications
```bash
cd frontend
.\start-all-frontends.bat
```

### Check Application Health
```bash
cd frontend
.\check-frontend-health.bat
```

### Individual Application Startup

#### Admin Dashboard
```bash
cd frontend/admin-dashboard
npm install
npm start
```

#### Live Operations Dashboard
```bash
cd frontend/live-operations-dashboard
npm install
set PORT=3001
npm start
```

#### Operator Dashboard
```bash
cd frontend/operator-dashboard
npm install
ng serve --port 4200
```

#### Driver App (Web Mode)
```bash
cd frontend/driver-app
npm install
npm run web
```

#### Passenger App (Web Mode)
```bash
cd frontend/passenger-app
npm install
expo start --web --port 19007
```

## 🔗 Backend Integration

All frontend applications are configured to connect to:
- **API Gateway:** http://localhost:8080
- **Authentication Service:** http://localhost:8081
- **WebSocket Endpoints:** Various ports for real-time features

## 📱 Mobile Development

### For Native Development:
1. Install Expo CLI: `npm install -g @expo/cli`
2. For Android: `npm run android`
3. For iOS: `npm run ios`

### For Web Development:
- Both mobile apps support web mode for development and testing
- Access via browsers at ports 19006 and 19007

## 🎨 UI/UX Features

### Design Systems:
- **Admin Dashboard:** Material-UI with custom theming
- **Live Operations:** Ant Design with dark theme support
- **Operator Dashboard:** Angular Material
- **Mobile Apps:** Native platform components

### Responsive Design:
- All web applications are fully responsive
- Mobile-first approach for mobile applications
- Cross-platform compatibility

## 🔧 Development Tools

### Available Scripts:
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run lint` - Code linting

### Development Features:
- Hot reload for all applications
- TypeScript support
- ESLint configuration
- Jest testing setup

## 📊 Monitoring & Analytics

### Real-time Features:
- WebSocket connections for live updates
- Real-time charts and graphs
- Live location tracking
- Instant notifications

### Performance Monitoring:
- Web Vitals tracking
- Bundle analysis tools
- Performance optimization

## 🔐 Security Features

### Authentication:
- JWT token integration
- Secure API communication
- Role-based access control
- Session management

### Data Protection:
- HTTPS enforcement
- Input validation
- XSS protection
- CSRF protection

## 🌐 Deployment Ready

### Production Build:
- Optimized bundles
- Code splitting
- Asset optimization
- Environment configuration

### Docker Support:
- Containerized deployments
- Multi-stage builds
- Production-ready images
