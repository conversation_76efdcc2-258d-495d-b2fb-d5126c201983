@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE HYBRID HEALTH CHECK
echo ========================================
echo.

set healthy=0
set total=8

echo 🐳 DOCKER SERVICES:
echo.

:: Check Eureka Server (Docker)
echo [1] Eureka Server (Docker - 8761):
curl -s -w "%%{http_code}" http://localhost:8761 -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Service Discovery running in Docker
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Fleet Service (Docker)
echo [2] Fleet Service (Docker - 8084):
curl -s -w "%%{http_code}" http://localhost:8084 -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Fleet service running in Docker
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Location Service (Docker)
echo [3] Location Service (Docker - 8085):
curl -s -w "%%{http_code}" http://localhost:8085 -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Location service running in Docker
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

echo.
echo 🚀 SPRING BOOT SERVICES:
echo.

:: Check API Gateway
echo [4] API Gateway (Spring Boot - 8080):
curl -s -w "%%{http_code}" http://localhost:8080/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - API Gateway running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Auth Service
echo [5] Auth Service (Spring Boot - 8081):
curl -s -w "%%{http_code}" http://localhost:8081/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Authentication service running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check User Service
echo [6] User Service (Spring Boot - 8082):
curl -s -w "%%{http_code}" http://localhost:8082/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - User service running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Payment Service
echo [7] Payment Service (Spring Boot - 8083):
curl -s -w "%%{http_code}" http://localhost:8083/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Payment service running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Ride Service
echo [8] Ride Service (Spring Boot - 8086):
curl -s -w "%%{http_code}" http://localhost:8086/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Ride service running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

echo.
echo ========================================
echo    HYBRID PLATFORM SUMMARY
echo ========================================
echo.
echo Healthy Services: %healthy%/%total%

if %healthy% geq 6 (
    echo.
    echo 🎉 HYBRID PLATFORM IS OPERATIONAL!
    echo.
    echo 🌐 Main Access Points:
    echo - Eureka Dashboard: http://localhost:8761
    echo - API Gateway: http://localhost:8080
    echo - pgAdmin: http://localhost:5050
    echo.
    echo 📊 Platform Status: READY FOR PRODUCTION
    echo.
    echo 🔗 Service Integration:
    echo - Docker services provide infrastructure
    echo - Spring Boot services provide business logic
    echo - All services registered with Eureka
) else (
    echo.
    echo ⚠️  PLATFORM NEEDS ATTENTION
    set /a unhealthy=%total%-%healthy%
    echo %unhealthy% services are not responding properly.
)

echo.
pause
