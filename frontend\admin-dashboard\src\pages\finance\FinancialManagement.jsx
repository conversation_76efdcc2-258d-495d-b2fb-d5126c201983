import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  Receipt,
  AccountBalance,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const FinancialManagement = () => {
  const [transactions] = useState([
    { id: 'TXN-001', type: 'Trip Payment', amount: 25.50, status: 'completed', date: '2025-01-30' },
    { id: 'TXN-002', type: 'Driver Payout', amount: -18.75, status: 'completed', date: '2025-01-30' },
    { id: 'TXN-003', type: 'Trip Payment', amount: 32.00, status: 'pending', date: '2025-01-30' },
  ]);

  const stats = {
    totalRevenue: 125450.75,
    monthlyRevenue: 15230.50,
    pendingPayouts: 8750.25,
    totalTransactions: 1247,
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Financial Management
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Revenue', value: `$${stats.totalRevenue.toLocaleString()}`, icon: <AttachMoney />, color: 'primary' },
          { title: 'Monthly Revenue', value: `$${stats.monthlyRevenue.toLocaleString()}`, icon: <TrendingUp />, color: 'success' },
          { title: 'Pending Payouts', value: `$${stats.pendingPayouts.toLocaleString()}`, icon: <AccountBalance />, color: 'warning' },
          { title: 'Total Transactions', value: stats.totalTransactions.toLocaleString(), icon: <Receipt />, color: 'info' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        {stat.title}
                      </Typography>
                      <Typography variant="h5" fontWeight="bold">
                        {stat.value}
                      </Typography>
                    </Box>
                    <Box sx={{ color: `${stat.color}.main` }}>
                      {stat.icon}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Transactions
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Transaction ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{transaction.id}</TableCell>
                    <TableCell>{transaction.type}</TableCell>
                    <TableCell>
                      <Typography
                        color={transaction.amount > 0 ? 'success.main' : 'error.main'}
                        fontWeight="bold"
                      >
                        ${Math.abs(transaction.amount).toFixed(2)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={transaction.status}
                        color={transaction.status === 'completed' ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FinancialManagement;
