version: '3.8'

services:
  # 🗄️ Database Services
  postgres-tecno:
    image: postgres:15-alpine
    container_name: postgres-tecno
    environment:
      POSTGRES_DB: tecnodrive
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
      POSTGRES_MULTIPLE_DATABASES: tecnodrive_auth,tecnodrive_users,tecnodrive_rides,tecnodrive_payments,tecnodrive_fleet,tecnodrive_parcels,tecnodrive_location,tecnodrive_analytics,tecnodrive_notifications,tecnodrive_hr,tecnodrive_financial,tecnodrive_wallet
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tecnodrive"]
      interval: 30s
      timeout: 10s
      retries: 3

  tecnodrive-redis:
    image: redis:7-alpine
    container_name: tecnodrive-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./database/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  tecnodrive-mongodb:
    image: mongo:7
    container_name: tecnodrive-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: secret
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  tecnodrive-timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tecnodrive-timescaledb
    environment:
      POSTGRES_DB: tecnodrive_timeseries
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tecnodrive_timeseries"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🌐 Infrastructure Services
  tecnodrive-eureka:
    build:
      context: ./backend/microservices/infrastructure/eureka-server
      dockerfile: Dockerfile
    container_name: tecnodrive-eureka
    ports:
      - "8761:8761"
    environment:
      SPRING_PROFILES_ACTIVE: docker
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  tecnodrive-api-gateway:
    build:
      context: ./backend/microservices/infrastructure/api-gateway
      dockerfile: Dockerfile
    container_name: tecnodrive-api-gateway
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  # 🔧 Core Microservices
  tecnodrive-auth:
    build:
      context: ./backend/microservices/core/auth-service
      dockerfile: Dockerfile
    container_name: tecnodrive-auth
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_auth
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-user:
    build:
      context: ./backend/microservices/core/user-service
      dockerfile: Dockerfile
    container_name: tecnodrive-user
    ports:
      - "8083:8083"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_users
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-ride:
    build:
      context: ./backend/microservices/business/ride-service
      dockerfile: Dockerfile
    container_name: tecnodrive-ride
    ports:
      - "8082:8082"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_rides
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-payment:
    build:
      context: ./backend/microservices/core/payment-service
      dockerfile: Dockerfile
    container_name: tecnodrive-payment
    ports:
      - "8086:8086"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_payments
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-parcel:
    build:
      context: ./backend/microservices/business/parcel-service
      dockerfile: Dockerfile
    container_name: tecnodrive-parcel
    ports:
      - "8087:8087"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_parcels
      SPRING_REDIS_HOST: tecnodrive-redis
      SPRING_MONGODB_URI: ***********************************************************************************
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-mongodb:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  # 🚗 Business Services
  tecnodrive-fleet:
    build:
      context: ./backend/microservices/business/fleet-service
      dockerfile: Dockerfile
    container_name: tecnodrive-fleet
    ports:
      - "8084:8084"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_fleet
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-location:
    build:
      context: ./backend/microservices/business/location-service
      dockerfile: Dockerfile
    container_name: tecnodrive-location
    ports:
      - "8085:8085"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: *******************************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      tecnodrive-timescaledb:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-analytics:
    build:
      context: ./backend/microservices/business/analytics-service
      dockerfile: Dockerfile
    container_name: tecnodrive-analytics
    ports:
      - "8088:8088"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_analytics
      SPRING_REDIS_HOST: tecnodrive-redis
      TIMESCALEDB_URL: *******************************************************************
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-timescaledb:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-notifications:
    build:
      context: ./backend/microservices/business/notification-service
      dockerfile: Dockerfile
    container_name: tecnodrive-notifications
    ports:
      - "8089:8089"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ************************************************_notifications
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  # 💼 Extended Business Services
  tecnodrive-hr:
    build:
      context: ./backend/microservices/business/hr-service
      dockerfile: Dockerfile
    container_name: tecnodrive-hr
    ports:
      - "8097:8097"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-financial:
    build:
      context: ./backend/microservices/business/financial-service
      dockerfile: Dockerfile
    container_name: tecnodrive-financial
    ports:
      - "8098:8098"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: **********************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-wallet:
    build:
      context: ./backend/microservices/business/wallet-service
      dockerfile: Dockerfile
    container_name: tecnodrive-wallet
    ports:
      - "8099:8099"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: *******************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  # 🎯 Operational Services
  tecnodrive-live-ops:
    build:
      context: ./backend/microservices/business/live-operations-service
      dockerfile: Dockerfile
    container_name: tecnodrive-live-ops
    ports:
      - "8100:8100"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: *********************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
      TIMESCALEDB_URL: *******************************************************************
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-timescaledb:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-operations:
    build:
      context: ./backend/microservices/business/operations-service
      dockerfile: Dockerfile
    container_name: tecnodrive-operations
    ports:
      - "8101:8101"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: ***********************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-tracking:
    build:
      context: ./backend/microservices/business/tracking-service
      dockerfile: Dockerfile
    container_name: tecnodrive-tracking
    ports:
      - "8102:8102"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: *******************************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
    depends_on:
      tecnodrive-timescaledb:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

  tecnodrive-demand:
    build:
      context: ./backend/microservices/business/demand-service
      dockerfile: Dockerfile
    container_name: tecnodrive-demand
    ports:
      - "8103:8103"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://tecnodrive-eureka:8761/eureka
      SPRING_DATASOURCE_URL: *******************************************************
      SPRING_REDIS_HOST: tecnodrive-redis
      TIMESCALEDB_URL: *******************************************************************
    depends_on:
      postgres-tecno:
        condition: service_healthy
      tecnodrive-timescaledb:
        condition: service_healthy
      tecnodrive-redis:
        condition: service_healthy
      tecnodrive-eureka:
        condition: service_healthy
    networks:
      - tecnodrive-network

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  timescaledb_data:

networks:
  tecnodrive-network:
    driver: bridge
