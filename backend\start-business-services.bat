@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    STARTING BUSINESS SERVICES
echo ========================================
echo.

echo [1/11] Starting Ride Service...
start "Ride Service" cmd /k "cd microservices\business\ride-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [2/11] Starting Fleet Service...
start "Fleet Service" cmd /k "cd microservices\business\fleet-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [3/11] Starting Location Service...
start "Location Service" cmd /k "cd microservices\business\location-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [4/11] Starting Analytics Service...
start "Analytics Service" cmd /k "cd microservices\business\analytics-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [5/11] Starting Notification Service...
start "Notification Service" cmd /k "cd microservices\business\notification-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [6/11] Starting Parcel Service...
start "Parcel Service" cmd /k "cd microservices\business\parcel-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [7/11] Starting Financial Service...
start "Financial Service" cmd /k "cd microservices\business\financial-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [8/11] Starting Wallet Service...
start "Wallet Service" cmd /k "cd microservices\business\wallet-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [9/11] Starting HR Service...
start "HR Service" cmd /k "cd microservices\business\hr-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [10/11] Starting Operations Service...
start "Operations Service" cmd /k "cd microservices\business\operations-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo [11/11] Starting Live Operations Service...
start "Live Operations Service" cmd /k "cd microservices\business\live-operations-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo    ALL BUSINESS SERVICES STARTED!
echo ========================================
echo.
echo Business Services:
echo - Ride Service: http://localhost:8084
echo - Fleet Service: http://localhost:8085
echo - Location Service: http://localhost:8086
echo - Analytics Service: http://localhost:8087
echo - Notification Service: http://localhost:8088
echo - Parcel Service: http://localhost:8089
echo - Financial Service: http://localhost:8090
echo - Wallet Service: http://localhost:8091
echo - HR Service: http://localhost:8092
echo - Operations Service: http://localhost:8093
echo - Live Operations Service: http://localhost:8094
echo.
echo Run health-check.bat to verify all services
echo.
pause
