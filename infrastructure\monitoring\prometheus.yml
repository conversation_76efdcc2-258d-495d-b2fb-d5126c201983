# Prometheus Configuration for TECNO DRIVE
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tecnodrive-local'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager-tecno:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Spring Boot Actuator endpoints
  - job_name: 'tecnodrive-services'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
    static_configs:
      - targets:
          - 'tecnodrive-eureka:8761'
          - 'tecnodrive-api-gateway:8080'
          - 'tecnodrive-auth:8081'
          - 'tecnodrive-user:8083'
          - 'tecnodrive-ride:8082'
          - 'tecnodrive-payment:8086'
          - 'tecnodrive-parcel:8087'
          - 'tecnodrive-fleet:8084'
          - 'tecnodrive-location:8085'
          - 'tecnodrive-analytics:8088'
          - 'tecnodrive-notifications:8089'
          - 'tecnodrive-hr:8097'
          - 'tecnodrive-financial:8098'
          - 'tecnodrive-wallet:8099'
          - 'tecnodrive-live-ops:8100'
          - 'tecnodrive-operations:8101'
          - 'tecnodrive-tracking:8102'
          - 'tecnodrive-demand:8103'

  # Database monitoring
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # RabbitMQ monitoring
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq-tecno:15692']

  # Custom business metrics
  - job_name: 'tecnodrive-business-metrics'
    metrics_path: '/metrics'
    scrape_interval: 30s
    static_configs:
      - targets:
          - 'tecnodrive-ride:8082'
          - 'tecnodrive-fleet:8084'
          - 'tecnodrive-analytics:8088'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '([^:]+):(.*)'
        target_label: service
        replacement: '${1}'
