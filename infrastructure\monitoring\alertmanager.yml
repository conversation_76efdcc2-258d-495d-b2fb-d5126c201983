# Alertmanager Configuration for TECNO DRIVE
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'alert_password'

# Templates
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route configuration
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m

    # Database alerts
    - match:
        service: database
      receiver: 'database-team'

    # Service alerts
    - match_re:
        service: tecnodrive-.*
      receiver: 'service-alerts'

    # Infrastructure alerts
    - match:
        category: infrastructure
      receiver: 'infrastructure-team'

# Inhibit rules
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']

# Receivers
receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/webhook'
        send_resolved: true

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }} in {{ .GroupLabels.service }}'
        body: |
          Alert: {{ .GroupLabels.alertname }}
          Service: {{ .GroupLabels.service }}
          Severity: {{ .CommonLabels.severity }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          Summary: {{ .Annotations.summary }}
          {{ end }}
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#critical-alerts'
        title: '🚨 Critical Alert'
        text: |
          *Alert:* {{ .GroupLabels.alertname }}
          *Service:* {{ .GroupLabels.service }}
          *Severity:* {{ .CommonLabels.severity }}

  - name: 'database-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️ Database Alert: {{ .GroupLabels.alertname }}'
        body: |
          Database Alert Details:
          
          Alert: {{ .GroupLabels.alertname }}
          Instance: {{ .CommonLabels.instance }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          {{ end }}

  - name: 'service-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ Service Alert: {{ .GroupLabels.alertname }}'
        body: |
          Service Alert:
          
          Service: {{ .GroupLabels.service }}
          Alert: {{ .GroupLabels.alertname }}
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          {{ end }}

  - name: 'infrastructure-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🏗️ Infrastructure Alert: {{ .GroupLabels.alertname }}'
        body: |
          Infrastructure Alert:
          
          Component: {{ .GroupLabels.alertname }}
          Cluster: {{ .GroupLabels.cluster }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          {{ end }}
