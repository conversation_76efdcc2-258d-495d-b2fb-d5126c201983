@echo off
echo ========================================
echo    FRONTEND APPLICATIONS STATUS
echo ========================================
echo.

echo [1] Admin Dashboard (Port 3000):
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Admin Dashboard running
) else (
    echo UNHEALTHY - Not responding
)

echo [2] Live Operations Dashboard (Port 3001):
curl -s http://localhost:3001 >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Live Operations running
) else (
    echo UNHEALTHY - Not responding
)

echo [3] Operator Dashboard (Port 4200):
curl -s http://localhost:4200 >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Operator Dashboard running
) else (
    echo UNHEALTHY - Not responding
)

echo [4] Driver App (Port 19006):
curl -s http://localhost:19006 >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Driver App running
) else (
    echo UNHEALTHY - Not responding
)

echo [5] Passenger App (Port 19007):
curl -s http://localhost:19007 >nul 2>&1
if %errorlevel% equ 0 (
    echo HEALTHY - Passenger App running
) else (
    echo UNHEALTHY - Not responding
)

echo.
echo ========================================
echo    FRONTEND ACCESS POINTS
echo ========================================
echo.
echo - Admin Dashboard: http://localhost:3000
echo - Live Operations: http://localhost:3001
echo - Operator Dashboard: http://localhost:4200
echo - Driver App: http://localhost:19006
echo - Passenger App: http://localhost:19007
echo.
pause
