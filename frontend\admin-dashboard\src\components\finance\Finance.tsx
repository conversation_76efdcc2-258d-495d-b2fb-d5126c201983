import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  Receipt,
  AccountBalance,
} from '@mui/icons-material';

const Finance: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Finance Management
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AttachMoney color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Revenue</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                $125,450
              </Typography>
              <Typography variant="body2" color="text.secondary">
                +12% from last month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Monthly Growth</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                +15.3%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Compared to last month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Receipt color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Pending Invoices</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                23
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total amount: $8,750
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AccountBalance color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Account Balance</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                $45,230
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Available funds
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button variant="contained" startIcon={<Receipt />}>
                  Generate Invoice
                </Button>
                <Button variant="outlined" startIcon={<AttachMoney />}>
                  Process Payment
                </Button>
                <Button variant="outlined" startIcon={<TrendingUp />}>
                  View Reports
                </Button>
                <Button variant="outlined" startIcon={<AccountBalance />}>
                  Manage Accounts
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Finance;
