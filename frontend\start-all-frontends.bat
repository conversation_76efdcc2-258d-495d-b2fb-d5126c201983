@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE FRONTEND APPLICATIONS
echo ========================================
echo.

echo Installing dependencies for all applications...
echo.

echo [1] Installing Admin Dashboard dependencies...
cd admin-dashboard
if not exist node_modules (
    echo Installing npm packages for Admin Dashboard...
    call npm install
) else (
    echo Dependencies already installed for Admin Dashboard
)
cd ..

echo.
echo [2] Installing Live Operations Dashboard dependencies...
cd live-operations-dashboard
if not exist node_modules (
    echo Installing npm packages for Live Operations Dashboard...
    call npm install
) else (
    echo Dependencies already installed for Live Operations Dashboard
)
cd ..

echo.
echo [3] Installing Operator Dashboard dependencies...
cd operator-dashboard
if not exist node_modules (
    echo Installing npm packages for Operator Dashboard...
    call npm install
) else (
    echo Dependencies already installed for Operator Dashboard
)
cd ..

echo.
echo [4] Installing Driver App dependencies...
cd driver-app
if not exist node_modules (
    echo Installing npm packages for Driver App...
    call npm install
) else (
    echo Dependencies already installed for Driver App
)
cd ..

echo.
echo [5] Installing Passenger App dependencies...
cd passenger-app
if not exist node_modules (
    echo Installing npm packages for Passenger App...
    call npm install
) else (
    echo Dependencies already installed for Passenger App
)
cd ..

echo.
echo [6] Installing Shared Components dependencies...
cd shared-components
if not exist node_modules (
    echo Installing npm packages for Shared Components...
    call npm install
) else (
    echo Dependencies already installed for Shared Components
)
cd ..

echo.
echo ========================================
echo    STARTING FRONTEND APPLICATIONS
echo ========================================
echo.

echo [1] Starting Admin Dashboard (Port 3000)...
start "Admin Dashboard" cmd /k "cd admin-dashboard && npm start"
timeout /t 5 /nobreak >nul

echo [2] Starting Live Operations Dashboard (Port 3001)...
start "Live Operations" cmd /k "cd live-operations-dashboard && set PORT=3001 && npm start"
timeout /t 5 /nobreak >nul

echo [3] Starting Operator Dashboard (Port 4200)...
start "Operator Dashboard" cmd /k "cd operator-dashboard && ng serve --port 4200"
timeout /t 5 /nobreak >nul

echo [4] Starting Driver App (Web Mode - Port 19006)...
start "Driver App" cmd /k "cd driver-app && npm run web"
timeout /t 5 /nobreak >nul

echo [5] Starting Passenger App (Web Mode - Port 19007)...
start "Passenger App" cmd /k "cd passenger-app && set EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 && expo start --web --port 19007"
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo    FRONTEND APPLICATIONS STARTED
echo ========================================
echo.
echo 🖥️  WEB DASHBOARDS:
echo - Admin Dashboard: http://localhost:3000
echo - Live Operations: http://localhost:3001  
echo - Operator Dashboard: http://localhost:4200
echo.
echo 📱 MOBILE APPS (Web Mode):
echo - Driver App: http://localhost:19006
echo - Passenger App: http://localhost:19007
echo.
echo ⏳ Please wait 2-3 minutes for all applications to fully load
echo 🔄 Applications will automatically open in your browser
echo.
pause
