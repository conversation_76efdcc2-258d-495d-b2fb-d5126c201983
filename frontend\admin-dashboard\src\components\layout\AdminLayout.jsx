import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  People,
  DirectionsCar,
  AttachMoney,
  LocalShipping,
  Analytics,
  Settings,
  Brightness4,
  Brightness7,
  ExitToApp,
  AccountCircle,
  Notifications,
  LiveTv,
  SupervisorAccount,
  FleetManagement,
  AccountBalance,
  Assessment,
  AdminPanelSettings,
  Security,
  Support,
  Business,
  School,
  TrendingUp,
  Speed,
  Timeline,
  PieChart,
  BarChart,
  MonitorHeart,
  NetworkCheck,
  Storage,
  Memory,
  Computer,
  CloudSync,
  DataUsage,
  AutoAwesome,
  SmartToy,
  Psychology,
  Insights,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { useThemeStore } from '../../store/themeStore';
import NotificationCenter from '../notifications/NotificationCenter';

const drawerWidth = 280;

const AdminLayout = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { user, logout } = useAuthStore();
  const { isDarkMode, toggleTheme } = useThemeStore();
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);

  // Navigation items with enhanced structure
  const navigationItems = [
    {
      title: 'Overview',
      items: [
        { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
        { text: 'Live Operations', icon: <LiveTv />, path: '/live-operations' },
        { text: 'Analytics', icon: <Analytics />, path: '/analytics' },
      ],
    },
    {
      title: 'Management',
      items: [
        { 
          text: 'User Management', 
          icon: <People />, 
          path: '/users',
          subItems: [
            { text: 'Basic Management', path: '/users' },
            { text: 'Advanced Management', path: '/users/advanced' },
          ],
        },
        { 
          text: 'Fleet Management', 
          icon: <DirectionsCar />, 
          path: '/fleet',
          subItems: [
            { text: 'Basic Fleet', path: '/fleet' },
            { text: 'Advanced Fleet', path: '/fleet/advanced' },
          ],
        },
        { 
          text: 'Financial Management', 
          icon: <AttachMoney />, 
          path: '/finance',
          subItems: [
            { text: 'Basic Finance', path: '/finance' },
            { text: 'Advanced Finance', path: '/finance/advanced' },
          ],
        },
        { text: 'Parcel Management', icon: <LocalShipping />, path: '/parcels' },
      ],
    },
    {
      title: 'Advanced Features',
      items: [
        { text: 'Smart Analytics', icon: <AutoAwesome />, path: '/analytics/smart' },
        { text: 'AI Insights', icon: <SmartToy />, path: '/analytics/ai' },
        { text: 'Predictive Analytics', icon: <Psychology />, path: '/analytics/predictive' },
        { text: 'Performance Monitoring', icon: <MonitorHeart />, path: '/monitoring' },
      ],
    },
    {
      title: 'System',
      items: [
        { text: 'System Settings', icon: <Settings />, path: '/settings' },
        { text: 'Security Center', icon: <Security />, path: '/security' },
        { text: 'Support Center', icon: <Support />, path: '/support' },
      ],
    },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleUserMenuOpen = (event) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = async () => {
    await logout();
    handleUserMenuClose();
    navigate('/login');
  };

  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and Brand */}
      <Box sx={{ p: 2, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h5" fontWeight="bold" color="primary">
          TECNO DRIVE
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Advanced Fleet Management
        </Typography>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {navigationItems.map((section, sectionIndex) => (
          <Box key={sectionIndex}>
            <Typography
              variant="overline"
              sx={{
                px: 2,
                py: 1,
                display: 'block',
                color: 'text.secondary',
                fontWeight: 'bold',
                fontSize: '0.75rem',
              }}
            >
              {section.title}
            </Typography>
            <List dense>
              {section.items.map((item, itemIndex) => (
                <Box key={itemIndex}>
                  <ListItem disablePadding>
                    <ListItemButton
                      onClick={() => handleNavigation(item.path)}
                      selected={isActiveRoute(item.path)}
                      sx={{
                        mx: 1,
                        borderRadius: 2,
                        '&.Mui-selected': {
                          backgroundColor: theme.palette.primary.main + '20',
                          color: theme.palette.primary.main,
                          '& .MuiListItemIcon-root': {
                            color: theme.palette.primary.main,
                          },
                        },
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText 
                        primary={item.text}
                        primaryTypographyProps={{
                          fontSize: '0.875rem',
                          fontWeight: isActiveRoute(item.path) ? 600 : 400,
                        }}
                      />
                      {item.subItems && (
                        <Badge badgeContent={item.subItems.length} color="secondary" />
                      )}
                    </ListItemButton>
                  </ListItem>
                  
                  {/* Sub-items */}
                  {item.subItems && isActiveRoute(item.path) && (
                    <List dense sx={{ pl: 2 }}>
                      {item.subItems.map((subItem, subIndex) => (
                        <ListItem key={subIndex} disablePadding>
                          <ListItemButton
                            onClick={() => handleNavigation(subItem.path)}
                            selected={location.pathname === subItem.path}
                            sx={{
                              mx: 1,
                              borderRadius: 1,
                              minHeight: 32,
                              '&.Mui-selected': {
                                backgroundColor: theme.palette.primary.main + '10',
                                color: theme.palette.primary.main,
                              },
                            }}
                          >
                            <ListItemText 
                              primary={subItem.text}
                              primaryTypographyProps={{
                                fontSize: '0.75rem',
                                fontWeight: location.pathname === subItem.path ? 600 : 400,
                              }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Box>
              ))}
            </List>
            {sectionIndex < navigationItems.length - 1 && <Divider sx={{ my: 1 }} />}
          </Box>
        ))}
      </Box>

      {/* User Profile Section */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: 'primary.main' }}>
            {user?.name?.charAt(0) || 'A'}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="subtitle2" noWrap>
              {user?.name || 'Admin User'}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              {user?.role || 'Administrator'}
            </Typography>
          </Box>
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
          Last login: {new Date().toLocaleDateString()}
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          zIndex: theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {navigationItems
              .flatMap(section => section.items)
              .find(item => isActiveRoute(item.path))?.text || 'Dashboard'}
          </Typography>

          {/* Notification Center */}
          <NotificationCenter />

          {/* Theme Toggle */}
          <Tooltip title="Toggle theme">
            <IconButton color="inherit" onClick={toggleTheme}>
              {isDarkMode ? <Brightness7 /> : <Brightness4 />}
            </IconButton>
          </Tooltip>

          {/* User Menu */}
          <Tooltip title="Account settings">
            <IconButton
              color="inherit"
              onClick={handleUserMenuOpen}
              sx={{ ml: 1 }}
            >
              <AccountCircle />
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      {/* Navigation Drawer */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <Toolbar />
        {children}
      </Box>

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { handleNavigation('/profile'); handleUserMenuClose(); }}>
          <AccountCircle sx={{ mr: 1 }} />
          Profile
        </MenuItem>
        <MenuItem onClick={() => { handleNavigation('/settings'); handleUserMenuClose(); }}>
          <Settings sx={{ mr: 1 }} />
          Settings
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ExitToApp sx={{ mr: 1 }} />
          Logout
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default AdminLayout;
