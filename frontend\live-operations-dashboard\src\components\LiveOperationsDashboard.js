import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Alert, Table, Tag, Progress } from 'antd';
import { 
  CarOutlined, 
  UserOutlined, 
  DollarOutlined, 
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import WebSocketService from '../services/WebSocketService';
import ApiService from '../services/ApiService';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const LiveOperationsDashboard = () => {
  const [dashboardData, setDashboardData] = useState({});
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load initial data
    loadDashboardData();

    // Setup WebSocket connection
    WebSocketService.connect();
    
    // Subscribe to real-time updates
    WebSocketService.subscribe('/topic/dashboard-updates', (data) => {
      setDashboardData(prev => ({ ...prev, ...data }));
    });

    WebSocketService.subscribe('/topic/alerts', (alert) => {
      setAlerts(prev => [alert, ...prev.slice(0, 9)]);
    });

    // Cleanup on unmount
    return () => {
      WebSocketService.disconnect();
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      const data = await ApiService.getDashboardData();
      setDashboardData(data);
      setAlerts(data.recentAlerts || []);
      setLoading(false);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setLoading(false);
    }
  };

  const fleetStatusData = {
    labels: ['Active', 'Idle', 'Maintenance', 'Offline'],
    datasets: [
      {
        data: [
          dashboardData.fleetStatus?.active || 0,
          dashboardData.fleetStatus?.idle || 0,
          dashboardData.fleetStatus?.maintenance || 0,
          dashboardData.fleetStatus?.offline || 0,
        ],
        backgroundColor: ['#52c41a', '#1890ff', '#faad14', '#ff4d4f'],
        borderWidth: 0,
      },
    ],
  };

  const alertColumns = [
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity) => {
        const color = severity === 'HIGH' ? 'red' : severity === 'MEDIUM' ? 'orange' : 'blue';
        return <Tag color={color}>{severity}</Tag>;
      },
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: 'Time',
      dataIndex: 'time',
      key: 'time',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[16, 16]}>
        {/* Key Metrics */}
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Rides"
              value={dashboardData.activeRides || 0}
              prefix={<CarOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Available Drivers"
              value={dashboardData.availableDrivers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Today's Revenue"
              value={dashboardData.revenue || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Avg Wait Time"
              value={dashboardData.averageWaitTime || '0 min'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        {/* Fleet Status Chart */}
        <Col xs={24} md={12}>
          <Card title="Fleet Status" loading={loading}>
            <div style={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Doughnut 
                data={fleetStatusData} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                    },
                  },
                }}
              />
            </div>
          </Card>
        </Col>

        {/* Performance Indicators */}
        <Col xs={24} md={12}>
          <Card title="Performance Indicators" loading={loading}>
            <div style={{ padding: '20px' }}>
              <div style={{ marginBottom: '20px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>Customer Satisfaction</span>
                  <span>{dashboardData.customerSatisfaction || 0}/5</span>
                </div>
                <Progress 
                  percent={(dashboardData.customerSatisfaction || 0) * 20} 
                  strokeColor="#52c41a"
                  showInfo={false}
                />
              </div>
              
              <div style={{ marginBottom: '20px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>Fleet Utilization</span>
                  <span>66%</span>
                </div>
                <Progress 
                  percent={66} 
                  strokeColor="#1890ff"
                  showInfo={false}
                />
              </div>
              
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>On-time Performance</span>
                  <span>94%</span>
                </div>
                <Progress 
                  percent={94} 
                  strokeColor="#722ed1"
                  showInfo={false}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        {/* Recent Alerts */}
        <Col xs={24} lg={16}>
          <Card title="Recent Alerts" loading={loading}>
            <Table
              dataSource={alerts}
              columns={alertColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* Quick Stats */}
        <Col xs={24} lg={8}>
          <Card title="Quick Stats" loading={loading}>
            <div style={{ padding: '10px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                <span>Completed Today:</span>
                <span style={{ fontWeight: 'bold' }}>{dashboardData.completedToday || 0}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                <span>Pending Requests:</span>
                <span style={{ fontWeight: 'bold', color: '#faad14' }}>{dashboardData.pendingRequests || 0}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                <span>Total Fleet:</span>
                <span style={{ fontWeight: 'bold' }}>{dashboardData.totalFleet || 0}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>System Status:</span>
                <span style={{ color: '#52c41a' }}>
                  <CheckCircleOutlined /> Online
                </span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default LiveOperationsDashboard;
