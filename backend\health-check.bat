@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE HEALTH CHECK
echo ========================================
echo.

set "services[0]=Eureka Server:8761:/actuator/health"
set "services[1]=Config Server:8888:/actuator/health"
set "services[2]=API Gateway:8080:/actuator/health"
set "services[3]=Monitoring Service:9090:/actuator/health"
set "services[4]=Auth Service:8081:/actuator/health"
set "services[5]=User Service:8082:/actuator/health"
set "services[6]=Payment Service:8083:/actuator/health"
set "services[7]=Ride Service:8084:/actuator/health"
set "services[8]=Fleet Service:8085:/actuator/health"
set "services[9]=Location Service:8086:/actuator/health"
set "services[10]=Analytics Service:8087:/actuator/health"
set "services[11]=Notification Service:8088:/actuator/health"
set "services[12]=Parcel Service:8089:/actuator/health"
set "services[13]=Financial Service:8090:/actuator/health"
set "services[14]=Wallet Service:8091:/actuator/health"
set "services[15]=HR Service:8092:/actuator/health"
set "services[16]=Operations Service:8093:/actuator/health"
set "services[17]=Live Operations Service:8094:/actuator/health"

set healthy=0
set total=18

echo Checking all services...
echo.

for /l %%i in (0,1,17) do (
    for /f "tokens=1,2,3 delims=:" %%a in ("!services[%%i]!") do (
        echo Checking %%a on port %%b...
        curl -s -o nul -w "%%{http_code}" http://localhost:%%b%%c > temp_status.txt
        set /p status=<temp_status.txt
        if "!status!"=="200" (
            echo ✅ %%a - HEALTHY
            set /a healthy+=1
        ) else (
            echo ❌ %%a - NOT RESPONDING ^(Status: !status!^)
        )
        del temp_status.txt 2>nul
    )
    echo.
)

echo ========================================
echo    HEALTH CHECK SUMMARY
echo ========================================
echo.
echo Healthy Services: %healthy%/%total%
echo.

if %healthy%==%total% (
    echo 🎉 ALL SERVICES ARE HEALTHY!
    echo Platform is ready for use.
) else (
    set /a unhealthy=%total%-%healthy%
    echo ⚠️  %unhealthy% services are not responding
    echo Please check the service logs for issues.
)

echo.
echo Service URLs:
echo - Eureka Dashboard: http://localhost:8761
echo - API Gateway: http://localhost:8080
echo - Monitoring: http://localhost:9090
echo.
pause
