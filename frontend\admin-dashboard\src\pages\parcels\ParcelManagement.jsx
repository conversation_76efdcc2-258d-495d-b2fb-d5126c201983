import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import {
  LocalShipping,
  Inventory,
  Schedule,
  CheckCircle,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const ParcelManagement = () => {
  const [parcels] = useState([
    { id: 'PCL-001', sender: '<PERSON>', recipient: '<PERSON>', status: 'in-transit', weight: '2.5kg' },
    { id: 'PCL-002', sender: 'ABC Corp', recipient: 'XYZ Ltd', status: 'delivered', weight: '5.0kg' },
    { id: 'PCL-003', sender: '<PERSON>', recipient: '<PERSON>', status: 'pending', weight: '1.2kg' },
  ]);

  const stats = {
    totalParcels: 1247,
    inTransit: 156,
    delivered: 1045,
    pending: 46,
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'in-transit': return 'info';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Parcel Management
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Parcels', value: stats.totalParcels, icon: <Inventory />, color: 'primary' },
          { title: 'In Transit', value: stats.inTransit, icon: <LocalShipping />, color: 'info' },
          { title: 'Delivered', value: stats.delivered, icon: <CheckCircle />, color: 'success' },
          { title: 'Pending', value: stats.pending, icon: <Schedule />, color: 'warning' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        {stat.title}
                      </Typography>
                      <Typography variant="h4" fontWeight="bold">
                        {stat.value}
                      </Typography>
                    </Box>
                    <Box sx={{ color: `${stat.color}.main` }}>
                      {stat.icon}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Parcels
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Parcel ID</TableCell>
                  <TableCell>Sender</TableCell>
                  <TableCell>Recipient</TableCell>
                  <TableCell>Weight</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {parcels.map((parcel) => (
                  <TableRow key={parcel.id}>
                    <TableCell>{parcel.id}</TableCell>
                    <TableCell>{parcel.sender}</TableCell>
                    <TableCell>{parcel.recipient}</TableCell>
                    <TableCell>{parcel.weight}</TableCell>
                    <TableCell>
                      <Chip
                        label={parcel.status}
                        color={getStatusColor(parcel.status)}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ParcelManagement;
