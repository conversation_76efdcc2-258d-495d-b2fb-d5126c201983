import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add,
  Edit,
  DirectionsCar,
  Build,
  LocationOn,
  Speed,
  LocalGasStation,
  Warning,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const FleetManagement = () => {
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = () => {
    const mockVehicles = Array.from({ length: 50 }, (_, i) => ({
      id: `VH-${String(i + 1).padStart(3, '0')}`,
      make: ['Toyota', 'Honda', 'Nissan', 'Hyundai'][Math.floor(Math.random() * 4)],
      model: ['Camry', 'Accord', 'Altima', 'Elantra'][Math.floor(Math.random() * 4)],
      year: 2018 + Math.floor(Math.random() * 6),
      status: ['active', 'maintenance', 'offline'][Math.floor(Math.random() * 3)],
      driver: Math.random() > 0.3 ? `Driver ${Math.floor(Math.random() * 100) + 1}` : 'Unassigned',
      location: `Zone ${Math.floor(Math.random() * 10) + 1}`,
      fuel: Math.floor(Math.random() * 100),
      mileage: Math.floor(Math.random() * 100000) + 10000,
      lastMaintenance: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      nextMaintenance: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    }));
    setVehicles(mockVehicles);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'maintenance': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const getFleetStats = () => {
    const total = vehicles.length;
    const active = vehicles.filter(v => v.status === 'active').length;
    const maintenance = vehicles.filter(v => v.status === 'maintenance').length;
    const offline = vehicles.filter(v => v.status === 'offline').length;
    const assigned = vehicles.filter(v => v.driver !== 'Unassigned').length;

    return { total, active, maintenance, offline, assigned };
  };

  const stats = getFleetStats();

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Fleet Management
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          Add Vehicle
        </Button>
      </Box>

      {/* Fleet Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Vehicles', value: stats.total, icon: <DirectionsCar />, color: 'primary' },
          { title: 'Active', value: stats.active, icon: <DirectionsCar />, color: 'success' },
          { title: 'In Maintenance', value: stats.maintenance, icon: <Build />, color: 'warning' },
          { title: 'Offline', value: stats.offline, icon: <Warning />, color: 'error' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        {stat.title}
                      </Typography>
                      <Typography variant="h4" fontWeight="bold">
                        {stat.value}
                      </Typography>
                    </Box>
                    <Box sx={{ color: `${stat.color}.main` }}>
                      {stat.icon}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Vehicle Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Vehicle Fleet
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Vehicle ID</TableCell>
                  <TableCell>Make/Model</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Driver</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Fuel</TableCell>
                  <TableCell>Mileage</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicles.slice(0, 15).map((vehicle) => (
                  <TableRow key={vehicle.id} hover>
                    <TableCell>{vehicle.id}</TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">
                          {vehicle.make} {vehicle.model}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {vehicle.year}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={vehicle.status}
                        color={getStatusColor(vehicle.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{vehicle.driver}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocationOn sx={{ mr: 0.5, fontSize: 16 }} />
                        {vehicle.location}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocalGasStation sx={{ mr: 0.5, fontSize: 16 }} />
                        {vehicle.fuel}%
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Speed sx={{ mr: 0.5, fontSize: 16 }} />
                        {vehicle.mileage.toLocaleString()} km
                      </Box>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedVehicle(vehicle);
                          setDialogOpen(true);
                        }}
                      >
                        <Edit />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Vehicle Details Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Vehicle Details - {selectedVehicle?.id}</DialogTitle>
        <DialogContent>
          {selectedVehicle && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Vehicle ID"
                  value={selectedVehicle.id}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Make"
                  value={selectedVehicle.make}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Model"
                  value={selectedVehicle.model}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Year"
                  value={selectedVehicle.year}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Status</InputLabel>
                  <Select value={selectedVehicle.status} label="Status">
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                    <MenuItem value="offline">Offline</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Assigned Driver"
                  value={selectedVehicle.driver}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Current Location"
                  value={selectedVehicle.location}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Fuel Level (%)"
                  value={selectedVehicle.fuel}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Mileage (km)"
                  value={selectedVehicle.mileage}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Maintenance"
                  value={selectedVehicle.lastMaintenance}
                  InputProps={{ readOnly: true }}
                  margin="normal"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Save Changes</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FleetManagement;
