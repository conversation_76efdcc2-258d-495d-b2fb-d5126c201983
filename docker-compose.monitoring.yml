version: '3.8'

services:
  # 🟡 Monitoring & Management Services
  pgadmin-tecno:
    image: dpage/pgadmin4:latest
    container_name: pgadmin-tecno
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./infrastructure/pgadmin/servers.json:/pgadmin4/servers.json
    networks:
      - tecnodrive-network
    depends_on:
      - postgres-tecno

  redis-insight-tecno:
    image: redislabs/redisinsight:latest
    container_name: redis-insight-tecno
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    networks:
      - tecnodrive-network
    depends_on:
      - tecnodrive-redis

  db-backup-tecno:
    image: postgres:15-alpine
    container_name: db-backup-tecno
    environment:
      POSTGRES_HOST: postgres-tecno
      POSTGRES_DB: tecnodrive
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM
    volumes:
      - backup_data:/backups
      - ./scripts/backup:/scripts
    command: >
      sh -c "
        echo '#!/bin/sh' > /scripts/backup.sh &&
        echo 'pg_dump -h postgres-tecno -U admin -d tecnodrive > /backups/backup_$(date +%Y%m%d_%H%M%S).sql' >> /scripts/backup.sh &&
        chmod +x /scripts/backup.sh &&
        crond -f
      "
    networks:
      - tecnodrive-network
    depends_on:
      - postgres-tecno

  # 📊 Monitoring Stack
  prometheus-tecno:
    image: prom/prometheus:latest
    container_name: prometheus-tecno
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tecnodrive-network

  grafana-tecno:
    image: grafana/grafana:latest
    container_name: grafana-tecno
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./infrastructure/monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - tecnodrive-network
    depends_on:
      - prometheus-tecno

  elasticsearch-tecno:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elasticsearch-tecno
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - tecnodrive-network

  kibana-tecno:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: kibana-tecno
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch-tecno:9200
    ports:
      - "5601:5601"
    networks:
      - tecnodrive-network
    depends_on:
      - elasticsearch-tecno

  logstash-tecno:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: logstash-tecno
    volumes:
      - ./infrastructure/monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./infrastructure/monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
    ports:
      - "5044:5044"
      - "9600:9600"
    networks:
      - tecnodrive-network
    depends_on:
      - elasticsearch-tecno

  # 📨 Message Queue
  rabbitmq-tecno:
    image: rabbitmq:3-management-alpine
    container_name: rabbitmq-tecno
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: secret
      RABBITMQ_DEFAULT_VHOST: tecnodrive
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./infrastructure/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - ./infrastructure/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    networks:
      - tecnodrive-network

  # 🔍 Distributed Tracing
  jaeger-tecno:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger-tecno
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    networks:
      - tecnodrive-network

  # 🚨 Alerting
  alertmanager-tecno:
    image: prom/alertmanager:latest
    container_name: alertmanager-tecno
    ports:
      - "9093:9093"
    volumes:
      - ./infrastructure/monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - tecnodrive-network

volumes:
  pgadmin_data:
  redis_insight_data:
  backup_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  rabbitmq_data:
  alertmanager_data:

networks:
  tecnodrive-network:
    external: true
