@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE FRONTEND STEP-BY-STEP
echo ========================================
echo.

echo This script will start frontend applications one by one
echo Each application will open in a new window
echo.

:menu
echo Choose an application to start:
echo.
echo [1] Admin Dashboard (React + Material-UI)
echo [2] Live Operations Dashboard (React + Ant Design)
echo [3] Operator Dashboard (Angular)
echo [4] Driver App (React Native Web)
echo [5] Passenger App (React Native Web)
echo [6] Start All Applications
echo [7] Check Application Status
echo [8] Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto admin
if "%choice%"=="2" goto live_ops
if "%choice%"=="3" goto operator
if "%choice%"=="4" goto driver
if "%choice%"=="5" goto passenger
if "%choice%"=="6" goto start_all
if "%choice%"=="7" goto check_status
if "%choice%"=="8" goto exit

echo Invalid choice. Please try again.
goto menu

:admin
echo.
echo Starting Admin Dashboard...
start "Admin Dashboard" cmd /k "cd admin-dashboard && npm install --legacy-peer-deps && npm start"
echo Admin Dashboard starting on http://localhost:3000
timeout /t 3 /nobreak >nul
goto menu

:live_ops
echo.
echo Starting Live Operations Dashboard...
start "Live Operations" cmd /k "cd live-operations-dashboard && npm install --legacy-peer-deps && set PORT=3001 && npm start"
echo Live Operations Dashboard starting on http://localhost:3001
timeout /t 3 /nobreak >nul
goto menu

:operator
echo.
echo Starting Operator Dashboard...
start "Operator Dashboard" cmd /k "cd operator-dashboard && npm install && ng serve --port 4200"
echo Operator Dashboard starting on http://localhost:4200
timeout /t 3 /nobreak >nul
goto menu

:driver
echo.
echo Starting Driver App...
start "Driver App" cmd /k "cd driver-app && npm install && npm run web"
echo Driver App starting on http://localhost:19006
timeout /t 3 /nobreak >nul
goto menu

:passenger
echo.
echo Starting Passenger App...
start "Passenger App" cmd /k "cd passenger-app && npm install && expo start --web --port 19007"
echo Passenger App starting on http://localhost:19007
timeout /t 3 /nobreak >nul
goto menu

:start_all
echo.
echo Starting all applications...
start "Admin Dashboard" cmd /k "cd admin-dashboard && npm install --legacy-peer-deps && npm start"
timeout /t 5 /nobreak >nul
start "Live Operations" cmd /k "cd live-operations-dashboard && npm install --legacy-peer-deps && set PORT=3001 && npm start"
timeout /t 5 /nobreak >nul
start "Operator Dashboard" cmd /k "cd operator-dashboard && npm install && ng serve --port 4200"
timeout /t 5 /nobreak >nul
echo All applications are starting...
echo Please wait 2-3 minutes for full initialization
goto menu

:check_status
echo.
echo Checking application status...
.\simple-frontend-check.bat
goto menu

:exit
echo.
echo Exiting...
exit /b 0
