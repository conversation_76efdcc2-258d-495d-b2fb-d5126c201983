import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import LiveOperationsDashboard from './components/LiveOperationsDashboard';
import FleetMonitor from './components/FleetMonitor';
import RideTracker from './components/RideTracker';
import AlertsPanel from './components/AlertsPanel';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import './App.css';

const { Content } = Layout;

function App() {
  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sidebar />
        <Layout>
          <Header />
          <Content style={{ margin: '16px', background: '#f0f2f5' }}>
            <Routes>
              <Route path="/" element={<LiveOperationsDashboard />} />
              <Route path="/dashboard" element={<LiveOperationsDashboard />} />
              <Route path="/fleet" element={<FleetMonitor />} />
              <Route path="/rides" element={<RideTracker />} />
              <Route path="/alerts" element={<AlertsPanel />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;
