package com.tecnodrive.gateway.controller;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * TECNO DRIVE - Gateway Controller
 *
 * Provides health checks and gateway information endpoints
 *
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/gateway")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"})
public class GatewayController {

    private final RouteLocator routeLocator;

    public GatewayController(RouteLocator routeLocator) {
        this.routeLocator = routeLocator;
    }

    /**
     * Gateway health check endpoint
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<Map<String, Object>>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "api-gateway");
        health.put("timestamp", LocalDateTime.now());
        health.put("version", "1.0.0");
        
        return Mono.just(ResponseEntity.ok(health));
    }

    /**
     * Get all configured routes
     */
    @GetMapping("/routes")
    public Mono<ResponseEntity<Map<String, Object>>> getRoutes() {
        return routeLocator.getRoutes()
            .collectList()
            .map(routes -> {
                Map<String, Object> response = new HashMap<>();
                response.put("total", routes.size());
                response.put("routes", routes.stream()
                    .map(route -> {
                        Map<String, Object> routeInfo = new HashMap<>();
                        routeInfo.put("id", route.getId());
                        routeInfo.put("uri", route.getUri().toString());
                        routeInfo.put("predicates", "Available");
                        routeInfo.put("filters", "Available");
                        return routeInfo;
                    })
                    .toList());
                return ResponseEntity.ok(response);
            });
    }

    /**
     * Gateway information endpoint
     */
    @GetMapping("/info")
    public Mono<ResponseEntity<Map<String, Object>>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "TECNO DRIVE API Gateway");
        info.put("description", "Central API Gateway for routing and authentication");
        info.put("version", "1.0.0");
        info.put("build", LocalDateTime.now());
        info.put("features", new String[]{
            "Request Routing",
            "Load Balancing", 
            "Authentication & Authorization",
            "Rate Limiting",
            "CORS Support",
            "Request/Response Logging",
            "Circuit Breaker",
            "Service Discovery"
        });
        
        return Mono.just(ResponseEntity.ok(info));
    }

    /**
     * Test endpoint for frontend connectivity
     */
    @GetMapping("/test")
    public Mono<ResponseEntity<Map<String, Object>>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "API Gateway is working!");
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");
        
        return Mono.just(ResponseEntity.ok(response));
    }

    /**
     * CORS preflight handler
     */
    @RequestMapping(method = RequestMethod.OPTIONS, value = "/**")
    public Mono<ResponseEntity<Void>> handleOptions() {
        return Mono.just(ResponseEntity.ok().build());
    }
}
