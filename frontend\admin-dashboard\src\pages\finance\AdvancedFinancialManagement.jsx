import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Chip,
  LinearProgress,
  Alert,
  Snackbar,
  Tooltip,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Badge,
  Menu,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Receipt,
  CreditCard,
  Payment,
  MonetizationOn,
  Analytics,
  Download,
  Upload,
  Refresh,
  Add,
  Edit,
  Delete,
  Visibility,
  Print,
  Share,
  FilterList,
  Search,
  CalendarToday,
  Assessment,
  PieChart,
  BarChart,
  Timeline,
  Warning,
  CheckCircle,
  Error,
  Info,
  Schedule,
  Business,
  School,
  Person,
  DirectionsCar,
  LocalShipping,
  ExpandMore,
  MoreVert,
  Notifications,
  Settings,
  Security,
  Sync,
  CloudSync,
  DataUsage,
  Storage,
  Computer,
  NetworkCheck,
  Memory,
  Speed,
  Timer,
  Star,
  Phone,
  Email,
  LocationOn,
  Assignment,
  Description,
  AttachFile,
  Webhook,
  Api,
  Integration,
  AutoAwesome,
  SmartToy,
  Psychology,
  Insights,
  TrendingFlat,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';
import { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';

const AdvancedFinancialManagement = () => {
  const theme = useTheme();
  const [transactions, setTransactions] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [commissions, setCommissions] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [invoiceDialog, setInvoiceDialog] = useState(false);
  const [reportDialog, setReportDialog] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [dateRange, setDateRange] = useState('month');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedItemForMenu, setSelectedItemForMenu] = useState(null);
  const [financialMetrics, setFinancialMetrics] = useState({});
  const [revenueData, setRevenueData] = useState({});
  const [expenseData, setExpenseData] = useState({});

  // Transaction types and configurations
  const transactionTypes = {
    trip_payment: { label: 'Trip Payment', icon: <DirectionsCar />, color: 'success' },
    delivery_payment: { label: 'Delivery Payment', icon: <LocalShipping />, color: 'info' },
    commission: { label: 'Commission', icon: <MonetizationOn />, color: 'warning' },
    refund: { label: 'Refund', icon: <Payment />, color: 'error' },
    withdrawal: { label: 'Driver Withdrawal', icon: <AccountBalance />, color: 'secondary' },
    subscription: { label: 'Subscription Fee', icon: <Receipt />, color: 'primary' },
    penalty: { label: 'Penalty', icon: <Warning />, color: 'error' },
    bonus: { label: 'Bonus', icon: <Star />, color: 'success' },
  };

  // Payment methods
  const paymentMethods = {
    credit_card: { label: 'Credit Card', icon: <CreditCard /> },
    debit_card: { label: 'Debit Card', icon: <CreditCard /> },
    bank_transfer: { label: 'Bank Transfer', icon: <AccountBalance /> },
    digital_wallet: { label: 'Digital Wallet', icon: <Payment /> },
    cash: { label: 'Cash', icon: <AttachMoney /> },
    apple_pay: { label: 'Apple Pay', icon: <Payment /> },
    google_pay: { label: 'Google Pay', icon: <Payment /> },
    stc_pay: { label: 'STC Pay', icon: <Payment /> },
  };

  // Invoice statuses
  const invoiceStatuses = {
    draft: { label: 'Draft', color: 'default', icon: <Edit /> },
    sent: { label: 'Sent', color: 'info', icon: <Email /> },
    paid: { label: 'Paid', color: 'success', icon: <CheckCircle /> },
    overdue: { label: 'Overdue', color: 'error', icon: <Warning /> },
    cancelled: { label: 'Cancelled', color: 'secondary', icon: <Error /> },
  };

  useEffect(() => {
    loadFinancialData();
    loadMetrics();
  }, []);

  useEffect(() => {
    filterData();
  }, [transactions, invoices, commissions, expenses, searchTerm, filterType, filterStatus, tabValue, dateRange]);

  const loadFinancialData = useCallback(async () => {
    setLoading(true);
    try {
      const mockTransactions = generateMockTransactions(500);
      const mockInvoices = generateMockInvoices(100);
      const mockCommissions = generateMockCommissions(200);
      const mockExpenses = generateMockExpenses(150);

      setTransactions(mockTransactions);
      setInvoices(mockInvoices);
      setCommissions(mockCommissions);
      setExpenses(mockExpenses);
    } catch (error) {
      showSnackbar('Error loading financial data', 'error');
    } finally {
      setLoading(false);
    }
  }, []);

  const generateMockTransactions = (count) => {
    const types = Object.keys(transactionTypes);
    const methods = Object.keys(paymentMethods);
    const statuses = ['completed', 'pending', 'failed', 'cancelled'];
    
    return Array.from({ length: count }, (_, i) => {
      const type = types[Math.floor(Math.random() * types.length)];
      const method = methods[Math.floor(Math.random() * methods.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const amount = Math.floor(Math.random() * 500) + 10;
      const date = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
      
      return {
        id: `TXN-${String(i + 1).padStart(6, '0')}`,
        type,
        amount,
        currency: 'SAR',
        status,
        method,
        date,
        description: `${transactionTypes[type].label} - ${amount} SAR`,
        customer: `Customer ${Math.floor(Math.random() * 1000) + 1}`,
        driver: type.includes('commission') || type.includes('withdrawal') ? `Driver ${Math.floor(Math.random() * 100) + 1}` : null,
        reference: `REF-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        fees: Math.floor(amount * 0.025), // 2.5% processing fee
        netAmount: amount - Math.floor(amount * 0.025),
        metadata: {
          tripId: type.includes('trip') ? `TRIP-${Math.floor(Math.random() * 10000)}` : null,
          orderId: type.includes('delivery') ? `ORDER-${Math.floor(Math.random() * 10000)}` : null,
          gateway: method === 'credit_card' || method === 'debit_card' ? 'Stripe' : 'Internal',
        },
      };
    });
  };

  const generateMockInvoices = (count) => {
    const statuses = Object.keys(invoiceStatuses);
    const clients = ['Riyadh Transport Co.', 'Jeddah Schools District', 'Dammam University', 'Mecca Hotels Group', 'Medina Medical Center'];
    
    return Array.from({ length: count }, (_, i) => {
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const client = clients[Math.floor(Math.random() * clients.length)];
      const amount = Math.floor(Math.random() * 50000) + 5000;
      const issueDate = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
      const dueDate = new Date(issueDate.getTime() + 30 * 24 * 60 * 60 * 1000);
      
      return {
        id: `INV-${String(i + 1).padStart(4, '0')}`,
        number: `2024-${String(i + 1).padStart(4, '0')}`,
        client,
        amount,
        currency: 'SAR',
        status,
        issueDate,
        dueDate,
        paidDate: status === 'paid' ? new Date(dueDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000) : null,
        description: `Monthly transportation services for ${client}`,
        items: [
          {
            description: 'Transportation Services',
            quantity: Math.floor(Math.random() * 1000) + 100,
            rate: Math.floor(Math.random() * 50) + 10,
            amount: amount * 0.8,
          },
          {
            description: 'Service Fees',
            quantity: 1,
            rate: amount * 0.2,
            amount: amount * 0.2,
          },
        ],
        tax: amount * 0.15, // 15% VAT
        totalAmount: amount + (amount * 0.15),
      };
    });
  };

  const generateMockCommissions = (count) => {
    return Array.from({ length: count }, (_, i) => {
      const amount = Math.floor(Math.random() * 1000) + 50;
      const date = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      
      return {
        id: `COMM-${String(i + 1).padStart(4, '0')}`,
        driverId: `DRV-${String(Math.floor(Math.random() * 500) + 1).padStart(3, '0')}`,
        driverName: `Driver ${Math.floor(Math.random() * 500) + 1}`,
        amount,
        rate: (Math.random() * 10 + 15).toFixed(1), // 15-25% commission rate
        totalEarnings: amount / (parseFloat((Math.random() * 10 + 15).toFixed(1)) / 100),
        date,
        status: Math.random() > 0.2 ? 'paid' : 'pending',
        trips: Math.floor(Math.random() * 50) + 10,
        period: 'weekly',
      };
    });
  };

  const generateMockExpenses = (count) => {
    const categories = ['fuel', 'maintenance', 'insurance', 'salaries', 'marketing', 'office', 'technology', 'legal'];
    
    return Array.from({ length: count }, (_, i) => {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const amount = Math.floor(Math.random() * 10000) + 500;
      const date = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
      
      return {
        id: `EXP-${String(i + 1).padStart(4, '0')}`,
        category,
        amount,
        currency: 'SAR',
        date,
        description: `${category.charAt(0).toUpperCase() + category.slice(1)} expense`,
        vendor: `Vendor ${Math.floor(Math.random() * 50) + 1}`,
        status: Math.random() > 0.1 ? 'approved' : 'pending',
        receipt: Math.random() > 0.2,
        approvedBy: Math.random() > 0.3 ? `Manager ${Math.floor(Math.random() * 10) + 1}` : null,
      };
    });
  };

  const loadMetrics = useCallback(() => {
    // Calculate financial metrics
    const totalRevenue = transactions
      .filter(t => ['trip_payment', 'delivery_payment', 'subscription'].includes(t.type) && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = expenses
      .filter(e => e.status === 'approved')
      .reduce((sum, e) => sum + e.amount, 0);

    const totalCommissions = commissions
      .filter(c => c.status === 'paid')
      .reduce((sum, c) => sum + c.amount, 0);

    const netProfit = totalRevenue - totalExpenses - totalCommissions;
    const profitMargin = totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(1) : 0;

    const pendingInvoices = invoices
      .filter(i => ['sent', 'overdue'].includes(i.status))
      .reduce((sum, i) => sum + i.totalAmount, 0);

    setFinancialMetrics({
      totalRevenue,
      totalExpenses,
      totalCommissions,
      netProfit,
      profitMargin,
      pendingInvoices,
      transactionCount: transactions.filter(t => t.status === 'completed').length,
      averageTransactionValue: totalRevenue / transactions.filter(t => t.status === 'completed').length || 0,
    });

    // Generate chart data
    generateChartData();
  }, [transactions, invoices, commissions, expenses]);

  const generateChartData = () => {
    // Revenue trend data
    const last12Months = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    }).reverse();

    const revenueByMonth = last12Months.map(month => {
      return Math.floor(Math.random() * 100000) + 50000;
    });

    setRevenueData({
      labels: last12Months,
      datasets: [
        {
          label: 'Revenue',
          data: revenueByMonth,
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.primary.light,
          tension: 0.4,
        },
      ],
    });

    // Expense breakdown data
    const expenseCategories = ['Fuel', 'Maintenance', 'Salaries', 'Marketing', 'Technology', 'Other'];
    const expenseAmounts = expenseCategories.map(() => Math.floor(Math.random() * 30000) + 10000);

    setExpenseData({
      labels: expenseCategories,
      datasets: [
        {
          data: expenseAmounts,
          backgroundColor: [
            theme.palette.error.main,
            theme.palette.warning.main,
            theme.palette.info.main,
            theme.palette.success.main,
            theme.palette.secondary.main,
            theme.palette.grey[500],
          ],
        },
      ],
    });
  };

  const filterData = useCallback(() => {
    let data = [];
    
    switch (tabValue) {
      case 0: // All Transactions
        data = transactions;
        break;
      case 1: // Invoices
        data = invoices;
        break;
      case 2: // Commissions
        data = commissions;
        break;
      case 3: // Expenses
        data = expenses;
        break;
      default:
        data = transactions;
    }

    // Apply filters
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      data = data.filter(item =>
        item.id?.toLowerCase().includes(term) ||
        item.description?.toLowerCase().includes(term) ||
        item.customer?.toLowerCase().includes(term) ||
        item.client?.toLowerCase().includes(term) ||
        item.driverName?.toLowerCase().includes(term)
      );
    }

    if (filterType !== 'all') {
      data = data.filter(item => item.type === filterType || item.category === filterType);
    }

    if (filterStatus !== 'all') {
      data = data.filter(item => item.status === filterStatus);
    }

    // Apply date range filter
    const now = new Date();
    let startDate;
    
    switch (dateRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(0);
    }

    data = data.filter(item => {
      const itemDate = item.date || item.issueDate;
      return itemDate >= startDate;
    });

    setFilteredData(data);
  }, [transactions, invoices, commissions, expenses, searchTerm, filterType, filterStatus, tabValue, dateRange]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleExport = (format = 'csv') => {
    const dataToExport = filteredData;
    
    if (format === 'csv') {
      const csvContent = dataToExport.map(item => {
        const date = item.date || item.issueDate;
        return `${item.id},${date.toISOString()},${item.amount || item.totalAmount},${item.status},${item.description || item.client}`;
      }).join('\n');
      
      const header = 'ID,Date,Amount,Status,Description\n';
      const blob = new Blob([header + csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `financial-report-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }
    
    showSnackbar('Report exported successfully', 'success');
  };

  const generateInvoice = (clientData) => {
    const newInvoice = {
      id: `INV-${String(invoices.length + 1).padStart(4, '0')}`,
      number: `2024-${String(invoices.length + 1).padStart(4, '0')}`,
      client: clientData.name,
      amount: clientData.amount,
      currency: 'SAR',
      status: 'draft',
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      description: clientData.description,
      items: clientData.items,
      tax: clientData.amount * 0.15,
      totalAmount: clientData.amount + (clientData.amount * 0.15),
    };

    setInvoices(prev => [newInvoice, ...prev]);
    showSnackbar('Invoice generated successfully', 'success');
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Advanced Financial Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<Assessment />}>
            Analytics
          </Button>
          <Button variant="outlined" startIcon={<Download />} onClick={() => handleExport('csv')}>
            Export
          </Button>
          <Button variant="outlined" startIcon={<Receipt />} onClick={() => setInvoiceDialog(true)}>
            Generate Invoice
          </Button>
          <Button variant="contained" startIcon={<Add />}>
            Add Transaction
          </Button>
        </Box>
      </Box>

      {/* Financial Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { 
            title: 'Total Revenue', 
            value: formatCurrency(financialMetrics.totalRevenue || 0), 
            icon: <TrendingUp />, 
            color: 'success',
            change: '+12.5%',
          },
          { 
            title: 'Total Expenses', 
            value: formatCurrency(financialMetrics.totalExpenses || 0), 
            icon: <TrendingDown />, 
            color: 'error',
            change: '+5.2%',
          },
          { 
            title: 'Net Profit', 
            value: formatCurrency(financialMetrics.netProfit || 0), 
            icon: <MonetizationOn />, 
            color: 'primary',
            change: '+18.7%',
          },
          { 
            title: 'Profit Margin', 
            value: `${financialMetrics.profitMargin || 0}%`, 
            icon: <Analytics />, 
            color: 'info',
            change: '+2.1%',
          },
          { 
            title: 'Pending Invoices', 
            value: formatCurrency(financialMetrics.pendingInvoices || 0), 
            icon: <Receipt />, 
            color: 'warning',
            change: '-8.3%',
          },
          { 
            title: 'Avg Transaction', 
            value: formatCurrency(financialMetrics.averageTransactionValue || 0), 
            icon: <Payment />, 
            color: 'secondary',
            change: '+4.6%',
          },
        ].map((metric, index) => (
          <Grid item xs={12} sm={6} md={2} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ color: `${metric.color}.main`, mb: 1 }}>
                    {metric.icon}
                  </Box>
                  <Typography variant="h6" fontWeight="bold" sx={{ fontSize: '0.9rem' }}>
                    {metric.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                    {metric.title}
                  </Typography>
                  <Typography 
                    variant="caption" 
                    color={metric.change.startsWith('+') ? 'success.main' : 'error.main'}
                    sx={{ fontSize: '0.7rem' }}
                  >
                    {metric.change}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue Trend
              </Typography>
              {revenueData.labels && (
                <Box sx={{ height: 300 }}>
                  <Line 
                    data={revenueData} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { display: false },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          ticks: {
                            callback: (value) => formatCurrency(value),
                          },
                        },
                      },
                    }}
                  />
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Expense Breakdown
              </Typography>
              {expenseData.labels && (
                <Box sx={{ height: 300 }}>
                  <Doughnut 
                    data={expenseData} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { position: 'bottom' },
                      },
                    }}
                  />
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  label="Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  {Object.entries(transactionTypes).map(([key, type]) => (
                    <MenuItem key={key} value={key}>{type.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Period</InputLabel>
                <Select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  label="Period"
                >
                  <MenuItem value="week">Last Week</MenuItem>
                  <MenuItem value="month">Last Month</MenuItem>
                  <MenuItem value="quarter">Last Quarter</MenuItem>
                  <MenuItem value="year">Last Year</MenuItem>
                  <MenuItem value="all">All Time</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button variant="outlined" startIcon={<Print />} fullWidth>
                  Print Report
                </Button>
                <Button variant="outlined" startIcon={<Share />} fullWidth>
                  Share
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Data Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label={`Transactions (${transactions.length})`} />
            <Tab label={`Invoices (${invoices.length})`} />
            <Tab label={`Commissions (${commissions.length})`} />
            <Tab label={`Expenses (${expenses.length})`} />
          </Tabs>
        </Box>

        <CardContent>
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  {tabValue === 0 && (
                    <>
                      <TableCell>Transaction</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Method</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </>
                  )}
                  {tabValue === 1 && (
                    <>
                      <TableCell>Invoice</TableCell>
                      <TableCell>Client</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Due Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </>
                  )}
                  {tabValue === 2 && (
                    <>
                      <TableCell>Driver</TableCell>
                      <TableCell>Commission</TableCell>
                      <TableCell>Rate</TableCell>
                      <TableCell>Trips</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </>
                  )}
                  {tabValue === 3 && (
                    <>
                      <TableCell>Expense</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Vendor</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Actions</TableCell>
                    </>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredData.slice(0, 20).map((item) => (
                  <TableRow key={item.id} hover>
                    {tabValue === 0 && (
                      <>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {item.id}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {item.customer}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {item.reference}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={transactionTypes[item.type]?.label}
                            color={transactionTypes[item.type]?.color}
                            size="small"
                            icon={transactionTypes[item.type]?.icon}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {formatCurrency(item.amount)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Net: {formatCurrency(item.netAmount)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={item.status}
                            color={
                              item.status === 'completed' ? 'success' :
                              item.status === 'pending' ? 'warning' :
                              item.status === 'failed' ? 'error' : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {paymentMethods[item.method]?.icon}
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {paymentMethods[item.method]?.label}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {item.date.toLocaleDateString()}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {item.date.toLocaleTimeString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              setMenuAnchor(e.currentTarget);
                              setSelectedItemForMenu(item);
                            }}
                          >
                            <MoreVert />
                          </IconButton>
                        </TableCell>
                      </>
                    )}
                    {/* Add similar table rows for other tabs */}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdvancedFinancialManagement;
