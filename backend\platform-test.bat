@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE PLATFORM ANALYSIS
echo ========================================
echo.

echo [1] SYSTEM REQUIREMENTS CHECK
echo ========================================
echo.

:: Check Java
echo Checking Java...
java -version 2>&1 | findstr "version"
if %errorlevel% neq 0 (
    echo ❌ Java not found
) else (
    echo ✅ Java is available
)

:: Check Maven
echo.
echo Checking Maven...
mvn -version 2>&1 | findstr "Apache Maven"
if %errorlevel% neq 0 (
    echo ❌ Maven not found
) else (
    echo ✅ Maven is available
)

echo.
echo [2] PROJECT STRUCTURE ANALYSIS
echo ========================================
echo.

echo Infrastructure Services:
if exist "microservices\infrastructure\eureka-server" (
    echo ✅ Eureka Server
) else (
    echo ❌ Eureka Server - Missing
)

if exist "microservices\infrastructure\config-server" (
    echo ✅ Config Server
) else (
    echo ❌ Config Server - Missing
)

if exist "microservices\infrastructure\api-gateway" (
    echo ✅ API Gateway
) else (
    echo ❌ API Gateway - Missing
)

if exist "microservices\infrastructure\monitoring-service" (
    echo ✅ Monitoring Service
) else (
    echo ❌ Monitoring Service - Missing
)

echo.
echo Core Services:
if exist "microservices\core\auth-service" (
    echo ✅ Auth Service
) else (
    echo ❌ Auth Service - Missing
)

if exist "microservices\core\user-service" (
    echo ✅ User Service
) else (
    echo ❌ User Service - Missing
)

if exist "microservices\core\payment-service" (
    echo ✅ Payment Service
) else (
    echo ❌ Payment Service - Missing
)

echo.
echo Business Services:
for %%s in (ride-service fleet-service location-service analytics-service notification-service parcel-service financial-service wallet-service hr-service operations-service live-operations-service) do (
    if exist "microservices\business\%%s" (
        echo ✅ %%s
    ) else (
        echo ❌ %%s - Missing
    )
)

echo.
echo [3] BUILD TEST
echo ========================================
echo.

echo Testing Maven build...
mvn clean compile -q -DskipTests
if %errorlevel% equ 0 (
    echo ✅ All services compile successfully
) else (
    echo ❌ Build failed - Check for compilation errors
)

echo.
echo [4] CONFIGURATION ANALYSIS
echo ========================================
echo.

echo Checking application.yml files...
set config_count=0
for /r "microservices" %%f in (application.yml) do (
    set /a config_count+=1
    echo Found: %%f
)
echo Total configuration files: %config_count%

echo.
echo [5] PORT AVAILABILITY CHECK
echo ========================================
echo.

echo Checking if ports are available...
set "ports=8761 8888 8080 9090 8081 8082 8083 8084 8085 8086 8087 8088 8089 8090 8091 8092 8093 8094"

for %%p in (%ports%) do (
    netstat -an | findstr ":%%p " >nul
    if !errorlevel! equ 0 (
        echo ❌ Port %%p is already in use
    ) else (
        echo ✅ Port %%p is available
    )
)

echo.
echo ========================================
echo    PLATFORM ANALYSIS COMPLETE
echo ========================================
echo.
echo Next steps:
echo 1. Fix any missing services or configuration issues
echo 2. Ensure all ports are available
echo 3. Run individual service tests
echo.
pause
