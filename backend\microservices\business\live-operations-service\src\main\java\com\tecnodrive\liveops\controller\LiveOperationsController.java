package com.tecnodrive.liveops.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Live Operations Controller
 * Handles real-time operations monitoring and management
 */
@RestController
@RequestMapping("/api/live-ops")
@CrossOrigin(origins = "*")
@EnableWebSocketMessageBroker
public class LiveOperationsController {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * Get real-time dashboard data
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardData() {
        Map<String, Object> dashboardData = new HashMap<>();
        
        // Real-time metrics
        dashboardData.put("activeRides", 45);
        dashboardData.put("availableDrivers", 23);
        dashboardData.put("totalFleet", 68);
        dashboardData.put("pendingRequests", 8);
        dashboardData.put("completedToday", 156);
        dashboardData.put("revenue", 2450.75);
        dashboardData.put("averageWaitTime", "3.2 min");
        dashboardData.put("customerSatisfaction", 4.7);
        
        // Fleet status
        Map<String, Integer> fleetStatus = new HashMap<>();
        fleetStatus.put("active", 45);
        fleetStatus.put("idle", 23);
        fleetStatus.put("maintenance", 3);
        fleetStatus.put("offline", 2);
        dashboardData.put("fleetStatus", fleetStatus);
        
        // Recent alerts
        List<Map<String, Object>> alerts = Arrays.asList(
            createAlert("HIGH", "Driver D001 exceeded speed limit", "2 min ago"),
            createAlert("MEDIUM", "High demand in Downtown area", "5 min ago"),
            createAlert("LOW", "Vehicle V045 due for maintenance", "10 min ago")
        );
        dashboardData.put("recentAlerts", alerts);
        
        // Live locations (sample data)
        List<Map<String, Object>> liveLocations = Arrays.asList(
            createLocation("D001", 24.7136, 46.6753, "BUSY", "Ride to Airport"),
            createLocation("D002", 24.7236, 46.6853, "AVAILABLE", "Waiting for ride"),
            createLocation("D003", 24.7036, 46.6653, "BUSY", "Ride to Mall")
        );
        dashboardData.put("liveLocations", liveLocations);
        
        dashboardData.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get real-time fleet status
     */
    @GetMapping("/fleet/status")
    public ResponseEntity<Map<String, Object>> getFleetStatus() {
        Map<String, Object> fleetData = new HashMap<>();
        
        // Fleet overview
        fleetData.put("totalVehicles", 68);
        fleetData.put("activeVehicles", 45);
        fleetData.put("utilizationRate", 66.2);
        
        // Vehicle types
        Map<String, Integer> vehicleTypes = new HashMap<>();
        vehicleTypes.put("sedan", 35);
        vehicleTypes.put("suv", 20);
        vehicleTypes.put("van", 8);
        vehicleTypes.put("motorcycle", 5);
        fleetData.put("vehicleTypes", vehicleTypes);
        
        // Performance metrics
        Map<String, Double> performance = new HashMap<>();
        performance.put("averageSpeed", 45.5);
        performance.put("fuelEfficiency", 12.8);
        performance.put("maintenanceScore", 8.7);
        fleetData.put("performance", performance);
        
        return ResponseEntity.ok(fleetData);
    }

    /**
     * Get live ride tracking data
     */
    @GetMapping("/rides/live")
    public ResponseEntity<List<Map<String, Object>>> getLiveRides() {
        List<Map<String, Object>> liveRides = Arrays.asList(
            createRideData("R001", "ACTIVE", "Downtown", "Airport", 15.5, "D001"),
            createRideData("R002", "PICKUP", "Mall", "University", 8.2, "D002"),
            createRideData("R003", "ACTIVE", "Hospital", "Hotel", 12.0, "D003")
        );
        
        return ResponseEntity.ok(liveRides);
    }

    /**
     * Send emergency alert
     */
    @PostMapping("/alerts/emergency")
    public ResponseEntity<String> sendEmergencyAlert(@RequestBody Map<String, Object> alertData) {
        // Process emergency alert
        Map<String, Object> alert = new HashMap<>();
        alert.put("type", "EMERGENCY");
        alert.put("message", alertData.get("message"));
        alert.put("location", alertData.get("location"));
        alert.put("timestamp", LocalDateTime.now());
        alert.put("severity", "CRITICAL");
        
        // Send to all connected clients via WebSocket
        messagingTemplate.convertAndSend("/topic/alerts", alert);
        
        return ResponseEntity.ok("Emergency alert sent successfully");
    }

    /**
     * Update driver status
     */
    @PutMapping("/drivers/{driverId}/status")
    public ResponseEntity<String> updateDriverStatus(
            @PathVariable String driverId, 
            @RequestBody Map<String, String> statusData) {
        
        // Update driver status
        Map<String, Object> update = new HashMap<>();
        update.put("driverId", driverId);
        update.put("status", statusData.get("status"));
        update.put("location", statusData.get("location"));
        update.put("timestamp", LocalDateTime.now());
        
        // Broadcast update via WebSocket
        messagingTemplate.convertAndSend("/topic/driver-updates", update);
        
        return ResponseEntity.ok("Driver status updated");
    }

    /**
     * Get operational statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getOperationalStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Today's statistics
        Map<String, Object> today = new HashMap<>();
        today.put("totalRides", 156);
        today.put("revenue", 2450.75);
        today.put("averageRideTime", 18.5);
        today.put("customerRating", 4.7);
        stats.put("today", today);
        
        // This week
        Map<String, Object> week = new HashMap<>();
        week.put("totalRides", 1089);
        week.put("revenue", 17234.50);
        week.put("newCustomers", 45);
        week.put("repeatCustomers", 234);
        stats.put("week", week);
        
        // Performance indicators
        Map<String, Double> kpis = new HashMap<>();
        kpis.put("onTimePerformance", 94.5);
        kpis.put("customerSatisfaction", 4.7);
        kpis.put("driverUtilization", 78.3);
        kpis.put("vehicleUtilization", 66.2);
        stats.put("kpis", kpis);
        
        return ResponseEntity.ok(stats);
    }

    // Helper methods
    private Map<String, Object> createAlert(String severity, String message, String time) {
        Map<String, Object> alert = new HashMap<>();
        alert.put("severity", severity);
        alert.put("message", message);
        alert.put("time", time);
        alert.put("id", UUID.randomUUID().toString());
        return alert;
    }

    private Map<String, Object> createLocation(String driverId, double lat, double lng, String status, String activity) {
        Map<String, Object> location = new HashMap<>();
        location.put("driverId", driverId);
        location.put("latitude", lat);
        location.put("longitude", lng);
        location.put("status", status);
        location.put("activity", activity);
        location.put("timestamp", LocalDateTime.now());
        return location;
    }

    private Map<String, Object> createRideData(String rideId, String status, String from, String to, double distance, String driverId) {
        Map<String, Object> ride = new HashMap<>();
        ride.put("rideId", rideId);
        ride.put("status", status);
        ride.put("from", from);
        ride.put("to", to);
        ride.put("distance", distance);
        ride.put("driverId", driverId);
        ride.put("estimatedTime", (int)(distance * 2.5));
        ride.put("timestamp", LocalDateTime.now());
        return ride;
    }
}
