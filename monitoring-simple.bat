@echo off
echo Starting Monitoring Services...

echo Starting Prometheus...
docker run -d --name prometheus-tecno -p 9090:9090 prom/prometheus:latest

echo Starting Grafana...
docker run -d --name grafana-tecno -p 3001:3000 -e GF_SECURITY_ADMIN_PASSWORD=admin123 grafana/grafana:latest

echo Starting Jaeger...
docker run -d --name jaeger-tecno -p 16686:16686 jaegertracing/all-in-one:latest

echo Starting Redis Insight...
docker run -d --name redis-insight-tecno -p 8001:8001 redislabs/redisinsight:latest

echo.
echo Monitoring services started!
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3001 (admin/admin123)
echo - Jaeger: http://localhost:16686
echo - Redis Insight: http://localhost:8001
echo.

docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
pause
