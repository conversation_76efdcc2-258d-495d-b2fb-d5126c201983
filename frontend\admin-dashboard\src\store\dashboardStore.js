import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { DashboardService } from '../services/DashboardService';

export const useDashboardStore = create(
  subscribeWithSelector((set, get) => ({
    // State
    operations: [],
    kpis: {
      activeRides: 0,
      availableDrivers: 0,
      revenue: 0,
      avgWaitTime: 0,
      completedToday: 0,
      pendingRequests: 0,
      totalFleet: 0,
      customerSatisfaction: 0,
      activeRidesTrend: 0,
      availableDriversTrend: 0,
      revenueTrend: 0,
      avgWaitTimeTrend: 0
    },
    alerts: [],
    systemStatus: {
      overall: 'healthy',
      services: {},
      lastCheck: null
    },
    isLoading: false,
    error: null,
    lastUpdate: null,
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds

    // Actions
    setLoading: (loading) => set({ isLoading: loading }),
    
    setError: (error) => set({ error }),
    
    setOperations: (operations) => set({ 
      operations,
      lastUpdate: new Date().toISOString()
    }),
    
    updateOperation: (updatedOperation) => set((state) => ({
      operations: state.operations.map(op => 
        op.id === updatedOperation.id ? { ...op, ...updatedOperation } : op
      ),
      lastUpdate: new Date().toISOString()
    })),
    
    addOperation: (newOperation) => set((state) => ({
      operations: [...state.operations, newOperation],
      lastUpdate: new Date().toISOString()
    })),
    
    removeOperation: (operationId) => set((state) => ({
      operations: state.operations.filter(op => op.id !== operationId),
      lastUpdate: new Date().toISOString()
    })),
    
    setKPIs: (kpis) => set({ kpis }),
    
    updateKPI: (key, value) => set((state) => ({
      kpis: { ...state.kpis, [key]: value }
    })),
    
    setAlerts: (alerts) => set({ alerts }),
    
    addAlert: (alert) => set((state) => ({
      alerts: [alert, ...state.alerts.slice(0, 49)] // Keep last 50 alerts
    })),
    
    removeAlert: (alertId) => set((state) => ({
      alerts: state.alerts.filter(alert => alert.id !== alertId)
    })),
    
    markAlertAsRead: (alertId) => set((state) => ({
      alerts: state.alerts.map(alert =>
        alert.id === alertId ? { ...alert, read: true } : alert
      )
    })),
    
    setSystemStatus: (status) => set({ systemStatus: status }),
    
    setAutoRefresh: (autoRefresh) => set({ autoRefresh }),
    
    setRefreshInterval: (interval) => set({ refreshInterval: interval }),

    // Async Actions
    fetchDashboardData: async () => {
      const { setLoading, setError, setOperations, setKPIs, setAlerts, setSystemStatus } = get();
      
      try {
        setLoading(true);
        setError(null);
        
        // Fetch all dashboard data in parallel
        const [operationsData, kpisData, alertsData, statusData] = await Promise.all([
          DashboardService.getOperations(),
          DashboardService.getKPIs(),
          DashboardService.getAlerts(),
          DashboardService.getSystemStatus()
        ]);
        
        setOperations(operationsData);
        setKPIs(kpisData);
        setAlerts(alertsData);
        setSystemStatus(statusData);
        
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    },
    
    refreshOperations: async () => {
      try {
        const operations = await DashboardService.getOperations();
        get().setOperations(operations);
      } catch (error) {
        console.error('Failed to refresh operations:', error);
        get().setError(error.message);
      }
    },
    
    refreshKPIs: async () => {
      try {
        const kpis = await DashboardService.getKPIs();
        get().setKPIs(kpis);
      } catch (error) {
        console.error('Failed to refresh KPIs:', error);
        get().setError(error.message);
      }
    },
    
    sendEmergencyAlert: async (alertData) => {
      try {
        const alert = await DashboardService.sendEmergencyAlert(alertData);
        get().addAlert(alert);
        return alert;
      } catch (error) {
        console.error('Failed to send emergency alert:', error);
        get().setError(error.message);
        throw error;
      }
    },
    
    optimizeRoute: async (vehicleId) => {
      try {
        const route = await DashboardService.optimizeRoute(vehicleId);
        return route;
      } catch (error) {
        console.error('Failed to optimize route:', error);
        get().setError(error.message);
        throw error;
      }
    },
    
    updateDriverStatus: async (driverId, status) => {
      try {
        await DashboardService.updateDriverStatus(driverId, status);
        // Update local operation if exists
        const operation = get().operations.find(op => op.driverId === driverId);
        if (operation) {
          get().updateOperation({ ...operation, driverStatus: status });
        }
      } catch (error) {
        console.error('Failed to update driver status:', error);
        get().setError(error.message);
        throw error;
      }
    },
    
    // Computed values
    getActiveOperationsCount: () => {
      return get().operations.filter(op => op.status === 'active').length;
    },
    
    getCriticalAlertsCount: () => {
      return get().alerts.filter(alert => 
        alert.severity === 'critical' && !alert.read
      ).length;
    },
    
    getOperationsByType: (type) => {
      return get().operations.filter(op => op.type === type);
    },
    
    getOperationsByStatus: (status) => {
      return get().operations.filter(op => op.status === status);
    },
    
    getUnreadAlertsCount: () => {
      return get().alerts.filter(alert => !alert.read).length;
    },
    
    // Filters and search
    filterOperations: (filters) => {
      const { operations } = get();
      return operations.filter(operation => {
        if (filters.type && operation.type !== filters.type) return false;
        if (filters.status && operation.status !== filters.status) return false;
        if (filters.driverId && operation.driverId !== filters.driverId) return false;
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          return (
            operation.driver_name?.toLowerCase().includes(searchLower) ||
            operation.customer_name?.toLowerCase().includes(searchLower) ||
            operation.id?.toLowerCase().includes(searchLower)
          );
        }
        return true;
      });
    },
    
    // Reset store
    reset: () => set({
      operations: [],
      kpis: {
        activeRides: 0,
        availableDrivers: 0,
        revenue: 0,
        avgWaitTime: 0,
        completedToday: 0,
        pendingRequests: 0,
        totalFleet: 0,
        customerSatisfaction: 0,
        activeRidesTrend: 0,
        availableDriversTrend: 0,
        revenueTrend: 0,
        avgWaitTimeTrend: 0
      },
      alerts: [],
      systemStatus: {
        overall: 'healthy',
        services: {},
        lastCheck: null
      },
      isLoading: false,
      error: null,
      lastUpdate: null
    })
  }))
);

// Auto-refresh subscription
let refreshTimer = null;

useDashboardStore.subscribe(
  (state) => state.autoRefresh,
  (autoRefresh) => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    
    if (autoRefresh) {
      const { refreshInterval, refreshOperations, refreshKPIs } = useDashboardStore.getState();
      refreshTimer = setInterval(() => {
        refreshOperations();
        refreshKPIs();
      }, refreshInterval);
    }
  }
);

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
  });
}
