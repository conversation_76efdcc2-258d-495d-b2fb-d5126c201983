@echo off
echo ========================================
echo   TECNO DRIVE - Starting Monitoring Stack
echo ========================================
echo.

echo [1/4] Starting Prometheus...
echo ========================================
docker run -d --name prometheus-tecno ^
  -p 9090:9090 ^
  -v "%cd%\infrastructure\monitoring\prometheus.yml:/etc/prometheus/prometheus.yml" ^
  --network bridge ^
  prom/prometheus:latest ^
  --config.file=/etc/prometheus/prometheus.yml ^
  --storage.tsdb.path=/prometheus ^
  --web.console.libraries=/etc/prometheus/console_libraries ^
  --web.console.templates=/etc/prometheus/consoles ^
  --storage.tsdb.retention.time=200h ^
  --web.enable-lifecycle

echo ✅ Prometheus started on port 9090
echo.

echo [2/4] Starting Grafana...
echo ========================================
docker run -d --name grafana-tecno ^
  -p 3001:3000 ^
  -e GF_SECURITY_ADMIN_USER=admin ^
  -e GF_SECURITY_ADMIN_PASSWORD=admin123 ^
  -e GF_USERS_ALLOW_SIGN_UP=false ^
  -v grafana-data:/var/lib/grafana ^
  --network bridge ^
  grafana/grafana:latest

echo ✅ Grafana started on port 3001
echo.

echo [3/4] Starting Elasticsearch & Kibana...
echo ========================================
docker run -d --name elasticsearch-tecno ^
  -p 9200:9200 ^
  -p 9300:9300 ^
  -e "discovery.type=single-node" ^
  -e "xpack.security.enabled=false" ^
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" ^
  --network bridge ^
  docker.elastic.co/elasticsearch/elasticsearch:8.11.0

timeout /t 30 /nobreak

docker run -d --name kibana-tecno ^
  -p 5601:5601 ^
  -e "ELASTICSEARCH_HOSTS=http://elasticsearch-tecno:9200" ^
  --network bridge ^
  docker.elastic.co/kibana/kibana:8.11.0

echo ✅ ELK Stack started
echo.

echo [4/4] Starting Additional Monitoring Tools...
echo ========================================

echo Starting Jaeger for distributed tracing...
docker run -d --name jaeger-tecno ^
  -p 16686:16686 ^
  -p 14268:14268 ^
  -p 14250:14250 ^
  -e COLLECTOR_OTLP_ENABLED=true ^
  --network bridge ^
  jaegertracing/all-in-one:latest

echo Starting Redis Insight...
docker run -d --name redis-insight-tecno ^
  -p 8001:8001 ^
  -v redis-insight-data:/db ^
  --network bridge ^
  redislabs/redisinsight:latest

echo ✅ Additional tools started
echo.

echo ========================================
echo   Monitoring Stack Ready!
echo ========================================
echo.
echo 📊 Access URLs:
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3001 (admin/admin123)
echo - Kibana: http://localhost:5601
echo - Elasticsearch: http://localhost:9200
echo - Jaeger: http://localhost:16686
echo - Redis Insight: http://localhost:8001
echo.
echo 🔍 Checking services...
timeout /t 10 /nobreak
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=tecno"
echo.
pause
