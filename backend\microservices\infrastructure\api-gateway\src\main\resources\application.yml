server:
  port: 8080

spring:
  application:
    name: api-gateway
  
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      routes:
        # Auth Service Routes
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - name: CircuitBreaker
              args:
                name: auth-service-cb
        
        # User Service Routes
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/users/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: user-service-cb
        
        # Payment Service Routes
        - id: payment-service
          uri: lb://payment-service
          predicates:
            - Path=/api/v1/payments/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: payment-service-cb

        # Ride Service Routes
        - id: ride-service
          uri: lb://ride-service
          predicates:
            - Path=/api/v1/rides/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: ride-service-cb

        # Fleet Service Routes
        - id: fleet-service
          uri: lb://fleet-service
          predicates:
            - Path=/api/v1/fleet/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: fleet-service-cb

        # Location Service Routes
        - id: location-service
          uri: lb://location-service
          predicates:
            - Path=/api/v1/locations/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: location-service-cb

        # Notification Service Routes
        - id: notification-service
          uri: lb://notification-service
          predicates:
            - Path=/api/v1/notifications/**
          filters:
            - AuthenticationFilter
            - name: CircuitBreaker
              args:
                name: notification-service-cb
      
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: 
              - "http://localhost:*"
              - "https://*.tecnodrive.com"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - PATCH
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

  # Redis configuration removed - using in-memory rate limiting

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    com.tecnodrive.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:your-super-secret-jwt-key-that-should-be-at-least-256-bits-long}

# Rate Limiting Configuration
rate-limit:
  default:
    replenish-rate: 10
    burst-capacity: 20
  authenticated:
    replenish-rate: 50
    burst-capacity: 100

---
spring:
  config:
    activate:
      on-profile: docker
  
  redis:
    host: redis

eureka:
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/
