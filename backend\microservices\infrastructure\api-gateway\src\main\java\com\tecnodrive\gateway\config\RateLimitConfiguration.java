package com.tecnodrive.gateway.config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * TECNO DRIVE - Rate Limiting Configuration
 * 
 * Configures rate limiting strategies for the API Gateway
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@Configuration
public class RateLimitConfiguration {

    /**
     * Rate limiting by IP address
     */
    @Bean
    @Primary
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            String clientIp = getClientIp(exchange);
            return Mono.just(clientIp);
        };
    }

    /**
     * Rate limiting by user ID (for authenticated requests)
     */
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            return Mono.just(userId != null ? userId : "anonymous");
        };
    }

    /**
     * Rate limiting by API key (for external integrations)
     */
    @Bean
    public KeyResolver apiKeyResolver() {
        return exchange -> {
            String apiKey = exchange.getRequest().getHeaders().getFirst("X-API-Key");
            return Mono.just(apiKey != null ? apiKey : "no-api-key");
        };
    }

    /**
     * Combined rate limiting by IP and User
     */
    @Bean
    public KeyResolver combinedKeyResolver() {
        return exchange -> {
            String clientIp = getClientIp(exchange);
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            String key = userId != null ? userId + ":" + clientIp : "anonymous:" + clientIp;
            return Mono.just(key);
        };
    }

    /**
     * Helper method to safely extract client IP address
     */
    private String getClientIp(ServerWebExchange exchange) {
        try {
            var remoteAddress = exchange.getRequest().getRemoteAddress();
            if (remoteAddress != null) {
                var address = remoteAddress.getAddress();
                if (address != null) {
                    return address.getHostAddress();
                }
            }
        } catch (Exception e) {
            // Log error if needed
        }
        return "unknown";
    }
}
