@echo off
echo ========================================
echo   TECNO DRIVE - System Health Check
echo ========================================
echo.

echo [1/5] Checking Container Status...
echo ========================================
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=tecnodrive"
echo.

echo [2/5] Checking Service Health...
echo ========================================

echo Checking Eureka Server...
curl -s -o nul -w "Eureka Server: %%{http_code}\n" http://localhost:8761/actuator/health

echo Checking API Gateway...
curl -s -o nul -w "API Gateway: %%{http_code}\n" http://localhost:8080/actuator/health

echo Checking Auth Service...
curl -s -o nul -w "Auth Service: %%{http_code}\n" http://localhost:8081/actuator/health

echo Checking User Service...
curl -s -o nul -w "User Service: %%{http_code}\n" http://localhost:8082/actuator/health

echo Checking Ride Service...
curl -s -o nul -w "Ride Service: %%{http_code}\n" http://localhost:8083/actuator/health

echo Checking Payment Service...
curl -s -o nul -w "Payment Service: %%{http_code}\n" http://localhost:8086/actuator/health

echo Checking Fleet Service...
curl -s -o nul -w "Fleet Service: %%{http_code}\n" http://localhost:8084/actuator/health

echo.

echo [3/5] Checking Database Connections...
echo ========================================

echo Checking PostgreSQL...
docker exec tecnodrive-postgres pg_isready -U admin -d tecnodrive
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL: Connected
) else (
    echo ❌ PostgreSQL: Connection Failed
)

echo Checking Redis...
docker exec tecnodrive-redis redis-cli ping
if %errorlevel% equ 0 (
    echo ✅ Redis: Connected
) else (
    echo ❌ Redis: Connection Failed
)

echo Checking MongoDB...
docker exec tecnodrive-mongodb mongosh --eval "db.adminCommand('ping')" --quiet
if %errorlevel% equ 0 (
    echo ✅ MongoDB: Connected
) else (
    echo ❌ MongoDB: Connection Failed
)

echo.

echo [4/5] Checking Resource Usage...
echo ========================================
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" --filter "name=tecnodrive"
echo.

echo [5/5] Service Registry Status...
echo ========================================
echo Checking registered services in Eureka...
curl -s http://localhost:8761/eureka/apps | findstr /i "application"
echo.

echo ========================================
echo   Health Check Complete
echo ========================================
echo.
echo Quick Access URLs:
echo - Eureka Dashboard: http://localhost:8761
echo - API Gateway: http://localhost:8080
echo - PgAdmin: http://localhost:5050
echo - Redis Insight: http://localhost:8001
echo - Grafana: http://localhost:3001
echo - Prometheus: http://localhost:9090
echo.
pause
