@echo off
echo ========================================
echo    STARTING ADMIN DASHBOARD
echo ========================================
echo.

cd admin-dashboard

echo Checking if dependencies are installed...
if not exist node_modules (
    echo Installing dependencies...
    npm install --legacy-peer-deps
) else (
    echo Dependencies already installed
)

echo.
echo Starting Admin Dashboard on port 3000...
echo.
echo This will open the Admin Dashboard in your browser
echo URL: http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

npm start
