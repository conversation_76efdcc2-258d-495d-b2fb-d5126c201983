server:
  port: 8100

spring:
  application:
    name: live-operations-service
  
  profiles:
    active: development
  
  datasource:
    url: ****************************************************
    username: admin
    password: secret
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8081

# Eureka Configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.tecnodrive.liveops: DEBUG
    org.springframework.web.socket: DEBUG
    org.springframework.messaging: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Custom Application Properties
tecnodrive:
  live-ops:
    websocket:
      allowed-origins: "*"
      heartbeat-interval: 25000
      disconnect-delay: 5000
    
    monitoring:
      update-interval: 5000
      alert-threshold:
        high-demand: 10
        driver-shortage: 5
        system-load: 80
    
    cache:
      dashboard-ttl: 30
      fleet-status-ttl: 60
      ride-data-ttl: 10
    
    integration:
      ride-service-url: http://localhost:8082
      fleet-service-url: http://localhost:8084
      user-service-url: http://localhost:8083
      notification-service-url: http://localhost:8089

---
# Docker Profile
spring:
  profiles: docker
  
  datasource:
    url: *********************************************************
  
  redis:
    host: tecnodrive-redis

eureka:
  client:
    service-url:
      defaultZone: http://tecnodrive-eureka:8761/eureka/

tecnodrive:
  live-ops:
    integration:
      ride-service-url: http://tecnodrive-ride:8082
      fleet-service-url: http://tecnodrive-fleet:8084
      user-service-url: http://tecnodrive-user:8083
      notification-service-url: http://tecnodrive-notifications:8089
