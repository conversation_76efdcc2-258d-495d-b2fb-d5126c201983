import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export const useThemeStore = create(
  persist(
    (set, get) => ({
      // State
      isDarkMode: false,
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      fontSize: 'medium',
      borderRadius: 12,
      compactMode: false,
      animations: true,
      language: 'en',
      rtl: false,

      // Theme variants
      themeVariants: {
        default: {
          primary: '#1976d2',
          secondary: '#dc004e',
          success: '#2e7d32',
          warning: '#ed6c02',
          error: '#d32f2f',
          info: '#0288d1',
        },
        blue: {
          primary: '#2196f3',
          secondary: '#ff4081',
          success: '#4caf50',
          warning: '#ff9800',
          error: '#f44336',
          info: '#00bcd4',
        },
        green: {
          primary: '#4caf50',
          secondary: '#e91e63',
          success: '#8bc34a',
          warning: '#ffc107',
          error: '#f44336',
          info: '#2196f3',
        },
        purple: {
          primary: '#9c27b0',
          secondary: '#ff5722',
          success: '#4caf50',
          warning: '#ff9800',
          error: '#f44336',
          info: '#2196f3',
        },
        orange: {
          primary: '#ff9800',
          secondary: '#3f51b5',
          success: '#4caf50',
          warning: '#ffc107',
          error: '#f44336',
          info: '#2196f3',
        },
      },

      // Actions
      toggleTheme: (forceDark = null) => {
        const newDarkMode = forceDark !== null ? forceDark : !get().isDarkMode;
        set({ isDarkMode: newDarkMode });
        
        // Update CSS custom properties
        document.documentElement.setAttribute('data-theme', newDarkMode ? 'dark' : 'light');
      },

      setThemeVariant: (variant) => {
        const { themeVariants } = get();
        if (themeVariants[variant]) {
          const colors = themeVariants[variant];
          set({
            primaryColor: colors.primary,
            secondaryColor: colors.secondary,
          });
          
          // Update CSS custom properties
          Object.entries(colors).forEach(([key, value]) => {
            document.documentElement.style.setProperty(`--color-${key}`, value);
          });
        }
      },

      setPrimaryColor: (color) => {
        set({ primaryColor: color });
        document.documentElement.style.setProperty('--color-primary', color);
      },

      setSecondaryColor: (color) => {
        set({ secondaryColor: color });
        document.documentElement.style.setProperty('--color-secondary', color);
      },

      setFontSize: (size) => {
        set({ fontSize: size });
        const sizeMap = {
          small: '14px',
          medium: '16px',
          large: '18px',
          xlarge: '20px',
        };
        document.documentElement.style.setProperty('--font-size-base', sizeMap[size]);
      },

      setBorderRadius: (radius) => {
        set({ borderRadius: radius });
        document.documentElement.style.setProperty('--border-radius', `${radius}px`);
      },

      toggleCompactMode: () => {
        const newCompactMode = !get().compactMode;
        set({ compactMode: newCompactMode });
        document.documentElement.setAttribute('data-compact', newCompactMode ? 'true' : 'false');
      },

      toggleAnimations: () => {
        const newAnimations = !get().animations;
        set({ animations: newAnimations });
        document.documentElement.setAttribute('data-animations', newAnimations ? 'true' : 'false');
      },

      setLanguage: (language) => {
        set({ language });
        document.documentElement.setAttribute('lang', language);
      },

      toggleRTL: () => {
        const newRTL = !get().rtl;
        set({ rtl: newRTL });
        document.documentElement.setAttribute('dir', newRTL ? 'rtl' : 'ltr');
      },

      resetTheme: () => {
        set({
          isDarkMode: false,
          primaryColor: '#1976d2',
          secondaryColor: '#dc004e',
          fontSize: 'medium',
          borderRadius: 12,
          compactMode: false,
          animations: true,
          language: 'en',
          rtl: false,
        });
        
        // Reset CSS custom properties
        document.documentElement.removeAttribute('data-theme');
        document.documentElement.removeAttribute('data-compact');
        document.documentElement.removeAttribute('data-animations');
        document.documentElement.setAttribute('lang', 'en');
        document.documentElement.setAttribute('dir', 'ltr');
      },

      initializeTheme: () => {
        const {
          isDarkMode,
          primaryColor,
          secondaryColor,
          fontSize,
          borderRadius,
          compactMode,
          animations,
          language,
          rtl,
        } = get();

        // Apply theme to DOM
        document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
        document.documentElement.setAttribute('data-compact', compactMode ? 'true' : 'false');
        document.documentElement.setAttribute('data-animations', animations ? 'true' : 'false');
        document.documentElement.setAttribute('lang', language);
        document.documentElement.setAttribute('dir', rtl ? 'rtl' : 'ltr');

        // Set CSS custom properties
        document.documentElement.style.setProperty('--color-primary', primaryColor);
        document.documentElement.style.setProperty('--color-secondary', secondaryColor);
        document.documentElement.style.setProperty('--border-radius', `${borderRadius}px`);
        
        const sizeMap = {
          small: '14px',
          medium: '16px',
          large: '18px',
          xlarge: '20px',
        };
        document.documentElement.style.setProperty('--font-size-base', sizeMap[fontSize]);
      },

      // Auto theme detection
      detectSystemTheme: () => {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const systemPrefersDark = mediaQuery.matches;
        
        // Only apply if user hasn't set a preference
        if (!localStorage.getItem('theme-storage')) {
          get().toggleTheme(systemPrefersDark);
        }

        // Listen for system theme changes
        const handleChange = (e) => {
          if (!localStorage.getItem('themePreference')) {
            get().toggleTheme(e.matches);
          }
        };

        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
      },

      // Export/Import theme settings
      exportTheme: () => {
        const {
          isDarkMode,
          primaryColor,
          secondaryColor,
          fontSize,
          borderRadius,
          compactMode,
          animations,
          language,
          rtl,
        } = get();

        return {
          isDarkMode,
          primaryColor,
          secondaryColor,
          fontSize,
          borderRadius,
          compactMode,
          animations,
          language,
          rtl,
          exportDate: new Date().toISOString(),
        };
      },

      importTheme: (themeConfig) => {
        try {
          const {
            isDarkMode,
            primaryColor,
            secondaryColor,
            fontSize,
            borderRadius,
            compactMode,
            animations,
            language,
            rtl,
          } = themeConfig;

          set({
            isDarkMode: isDarkMode ?? get().isDarkMode,
            primaryColor: primaryColor ?? get().primaryColor,
            secondaryColor: secondaryColor ?? get().secondaryColor,
            fontSize: fontSize ?? get().fontSize,
            borderRadius: borderRadius ?? get().borderRadius,
            compactMode: compactMode ?? get().compactMode,
            animations: animations ?? get().animations,
            language: language ?? get().language,
            rtl: rtl ?? get().rtl,
          });

          get().initializeTheme();
          return { success: true };
        } catch (error) {
          console.error('Theme import failed:', error);
          return { success: false, error: error.message };
        }
      },
    }),
    {
      name: 'theme-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
