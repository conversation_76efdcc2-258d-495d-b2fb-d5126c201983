import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
} from '@mui/material';
import {
  People,
  PersonAdd,
  Schedule,
  Assignment,
  TrendingUp,
  Work,
  School,
  Star,
} from '@mui/icons-material';

const HR: React.FC = () => {
  const recentHires = [
    { name: '<PERSON>', position: 'Driver', date: '2024-01-28' },
    { name: '<PERSON><PERSON>', position: 'Dispatcher', date: '2024-01-27' },
    { name: '<PERSON>', position: 'Mechanic', date: '2024-01-25' },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Human Resources Management
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <People color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Employees</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                248
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active workforce
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonAdd color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">New Hires</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                12
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Schedule color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">On Leave</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                18
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Currently absent
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Star color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Performance</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                4.2
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average rating
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Department Overview
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 3 }}>
                <Chip 
                  icon={<Work />} 
                  label="Drivers: 180" 
                  color="primary" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<Assignment />} 
                  label="Dispatchers: 25" 
                  color="secondary" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<School />} 
                  label="Mechanics: 20" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<People />} 
                  label="Admin: 23" 
                  color="info" 
                  variant="outlined" 
                />
              </Box>
              
              <Typography variant="h6" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button variant="contained" startIcon={<PersonAdd />}>
                  Add Employee
                </Button>
                <Button variant="outlined" startIcon={<Schedule />}>
                  Manage Schedules
                </Button>
                <Button variant="outlined" startIcon={<Assignment />}>
                  Performance Review
                </Button>
                <Button variant="outlined" startIcon={<TrendingUp />}>
                  HR Analytics
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Recent Hires
              </Typography>
              <List>
                {recentHires.map((hire, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {hire.name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={hire.name}
                      secondary={`${hire.position} • ${hire.date}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default HR;
