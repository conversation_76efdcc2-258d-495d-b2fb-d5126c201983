@echo off
echo ========================================
echo   TECNO DRIVE - System Status Check
echo ========================================
echo.

echo [1] Container Status:
echo ========================================
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.

echo [2] Service Health Check:
echo ========================================

echo Testing PostgreSQL...
docker exec postgres-tecno pg_isready -U admin -d tecnodrive 2>nul
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL: Running
) else (
    echo ❌ PostgreSQL: Not responding
)

echo Testing Redis...
docker exec tecnodrive-redis redis-cli ping 2>nul
if %errorlevel% equ 0 (
    echo ✅ Redis: Running
) else (
    echo ❌ Redis: Not responding
)

echo.
echo [3] Service URLs:
echo ========================================
echo 🗄️ Database Services:
echo - PostgreSQL: localhost:5432
echo - Redis: localhost:6379
echo.
echo 🔧 Core Services:
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8083  
echo - Ride Service: http://localhost:8082
echo - Payment Service: http://localhost:8086
echo - Parcel Service: http://localhost:8087
echo.
echo 📊 Monitoring (if running):
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3001
echo.

echo [4] Resource Usage:
echo ========================================
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"
echo.

echo ========================================
echo   Status Check Complete
echo ========================================
pause
