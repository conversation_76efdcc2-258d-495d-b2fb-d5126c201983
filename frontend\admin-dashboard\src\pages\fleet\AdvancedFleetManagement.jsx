import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  LinearProgress,
  Alert,
  Snackbar,
  Tooltip,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Paper,
  Menu,
} from '@mui/material';
import {
  Add,
  Edit,
  DirectionsCar,
  Build,
  LocationOn,
  Speed,
  LocalGasStation,
  Warning,
  CheckCircle,
  Error,
  Schedule,
  Refresh,
  Download,
  Upload,
  Map,
  Timeline,
  Assessment,
  Notifications,
  Settings,
  ExpandMore,
  MoreVert,
  CarRepair,
  CarRental,
  Commute,
  Traffic,
  Route,
  Gps,
  Battery80,
  SignalWifi4Bar,
  Timer,
  Star,
  Phone,
  Emergency,
  Security,
  Analytics,
  MonitorHeart,
  NetworkCheck,
  Storage,
  Memory,
  Computer,
  CloudSync,
  DataUsage,
  FilterList,
  Search,
  CalendarToday,
  Assignment,
  Description,
  AttachFile,
  Visibility,
  Print,
  Share,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

const AdvancedFleetManagement = () => {
  const theme = useTheme();
  const [vehicles, setVehicles] = useState([]);
  const [filteredVehicles, setFilteredVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [maintenanceDialog, setMaintenanceDialog] = useState(false);
  const [documentsDialog, setDocumentsDialog] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedVehicleForMenu, setSelectedVehicleForMenu] = useState(null);
  const [maintenanceSchedule, setMaintenanceSchedule] = useState([]);
  const [realTimeData, setRealTimeData] = useState({});

  // Vehicle types and configurations
  const vehicleTypes = {
    sedan: { label: 'Sedan', icon: '🚗', capacity: 4, fuelType: 'gasoline' },
    suv: { label: 'SUV', icon: '🚙', capacity: 7, fuelType: 'gasoline' },
    van: { label: 'Van', icon: '🚐', capacity: 12, fuelType: 'diesel' },
    truck: { label: 'Truck', icon: '🚚', capacity: 2, fuelType: 'diesel' },
    electric: { label: 'Electric', icon: '⚡', capacity: 4, fuelType: 'electric' },
    hybrid: { label: 'Hybrid', icon: '🔋', capacity: 4, fuelType: 'hybrid' },
  };

  // Vehicle status configurations
  const statusConfig = {
    active: { label: 'Active', color: 'success', icon: <CheckCircle /> },
    maintenance: { label: 'Maintenance', color: 'warning', icon: <Build /> },
    offline: { label: 'Offline', color: 'error', icon: <Error /> },
    reserved: { label: 'Reserved', color: 'info', icon: <Schedule /> },
    inspection: { label: 'Inspection', color: 'secondary', icon: <Assignment /> },
  };

  // Maintenance types
  const maintenanceTypes = {
    routine: { label: 'Routine Service', priority: 'low', interval: 5000 },
    oil_change: { label: 'Oil Change', priority: 'medium', interval: 3000 },
    tire_rotation: { label: 'Tire Rotation', priority: 'low', interval: 8000 },
    brake_service: { label: 'Brake Service', priority: 'high', interval: 15000 },
    transmission: { label: 'Transmission Service', priority: 'high', interval: 30000 },
    inspection: { label: 'Safety Inspection', priority: 'critical', interval: 12000 },
  };

  useEffect(() => {
    loadVehicles();
    loadMaintenanceSchedule();
    startRealTimeUpdates();
  }, []);

  useEffect(() => {
    filterVehicles();
  }, [vehicles, searchTerm, filterStatus, filterType, tabValue]);

  const loadVehicles = useCallback(async () => {
    setLoading(true);
    try {
      const mockVehicles = generateMockVehicles(75);
      setVehicles(mockVehicles);
    } catch (error) {
      showSnackbar('Error loading vehicles', 'error');
    } finally {
      setLoading(false);
    }
  }, []);

  const generateMockVehicles = (count) => {
    const types = Object.keys(vehicleTypes);
    const statuses = Object.keys(statusConfig);
    const makes = ['Toyota', 'Honda', 'Nissan', 'Hyundai', 'Ford', 'Chevrolet', 'BMW', 'Mercedes'];
    const models = ['Camry', 'Accord', 'Altima', 'Elantra', 'Focus', 'Cruze', 'X3', 'C-Class'];
    const cities = ['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina'];

    return Array.from({ length: count }, (_, i) => {
      const type = types[Math.floor(Math.random() * types.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const make = makes[Math.floor(Math.random() * makes.length)];
      const model = models[Math.floor(Math.random() * models.length)];
      const year = 2018 + Math.floor(Math.random() * 6);
      const mileage = Math.floor(Math.random() * 150000) + 10000;
      const lastMaintenance = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
      
      return {
        id: `VH-${String(i + 1).padStart(3, '0')}`,
        plateNumber: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}-${Math.floor(Math.random() * 9000) + 1000}`,
        make,
        model,
        year,
        type,
        status,
        driver: Math.random() > 0.3 ? `Driver ${Math.floor(Math.random() * 100) + 1}` : null,
        location: {
          city: cities[Math.floor(Math.random() * cities.length)],
          lat: 24.7136 + (Math.random() - 0.5) * 0.1,
          lng: 46.6753 + (Math.random() - 0.5) * 0.1,
          address: `Street ${Math.floor(Math.random() * 100) + 1}`,
        },
        fuel: Math.floor(Math.random() * 100),
        battery: type === 'electric' || type === 'hybrid' ? Math.floor(Math.random() * 100) : null,
        mileage,
        lastMaintenance,
        nextMaintenance: new Date(lastMaintenance.getTime() + (Math.random() * 30 + 15) * 24 * 60 * 60 * 1000),
        maintenanceHistory: generateMaintenanceHistory(),
        documents: {
          registration: {
            status: Math.random() > 0.1 ? 'valid' : 'expired',
            expiryDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
          },
          insurance: {
            status: Math.random() > 0.05 ? 'valid' : 'expired',
            expiryDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
          },
          inspection: {
            status: Math.random() > 0.15 ? 'valid' : 'expired',
            expiryDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
          },
        },
        performance: {
          fuelEfficiency: (Math.random() * 10 + 8).toFixed(1), // km/l
          averageSpeed: Math.floor(Math.random() * 40) + 30, // km/h
          utilizationRate: (Math.random() * 40 + 60).toFixed(1), // %
          maintenanceCost: Math.floor(Math.random() * 5000) + 1000, // SAR
        },
        sensors: {
          engine: Math.random() > 0.95 ? 'warning' : 'normal',
          brakes: Math.random() > 0.98 ? 'critical' : 'normal',
          transmission: Math.random() > 0.97 ? 'warning' : 'normal',
          tires: Math.random() > 0.9 ? 'warning' : 'normal',
          battery: Math.random() > 0.92 ? 'warning' : 'normal',
        },
        connectivity: {
          gps: Math.random() > 0.02,
          cellular: Math.random() > 0.05,
          wifi: Math.random() > 0.1,
          bluetooth: Math.random() > 0.03,
        },
        alerts: generateVehicleAlerts(),
      };
    });
  };

  const generateMaintenanceHistory = () => {
    const types = Object.keys(maintenanceTypes);
    const history = [];
    
    for (let i = 0; i < Math.floor(Math.random() * 10) + 5; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const date = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
      
      history.push({
        id: `maint-${i + 1}`,
        type,
        date,
        cost: Math.floor(Math.random() * 2000) + 200,
        description: `${maintenanceTypes[type].label} performed`,
        technician: `Tech ${Math.floor(Math.random() * 10) + 1}`,
        status: 'completed',
      });
    }
    
    return history.sort((a, b) => b.date - a.date);
  };

  const generateVehicleAlerts = () => {
    const alerts = [];
    
    if (Math.random() > 0.8) {
      alerts.push({
        id: 'alert-1',
        type: 'maintenance',
        severity: 'warning',
        message: 'Maintenance due in 500km',
        timestamp: new Date(),
      });
    }
    
    if (Math.random() > 0.9) {
      alerts.push({
        id: 'alert-2',
        type: 'fuel',
        severity: 'info',
        message: 'Fuel level below 25%',
        timestamp: new Date(),
      });
    }
    
    if (Math.random() > 0.95) {
      alerts.push({
        id: 'alert-3',
        type: 'engine',
        severity: 'critical',
        message: 'Engine temperature high',
        timestamp: new Date(),
      });
    }
    
    return alerts;
  };

  const loadMaintenanceSchedule = useCallback(() => {
    // Generate upcoming maintenance schedule
    const schedule = vehicles.flatMap(vehicle => {
      const maintenanceItems = [];
      const nextDate = vehicle.nextMaintenance;
      
      if (nextDate && nextDate > new Date()) {
        const daysUntil = Math.ceil((nextDate - new Date()) / (1000 * 60 * 60 * 24));
        
        if (daysUntil <= 30) {
          maintenanceItems.push({
            vehicleId: vehicle.id,
            vehicleName: `${vehicle.make} ${vehicle.model} (${vehicle.plateNumber})`,
            type: 'routine',
            scheduledDate: nextDate,
            daysUntil,
            priority: daysUntil <= 7 ? 'critical' : daysUntil <= 14 ? 'high' : 'medium',
            estimatedCost: Math.floor(Math.random() * 1500) + 500,
          });
        }
      }
      
      return maintenanceItems;
    });
    
    setMaintenanceSchedule(schedule.sort((a, b) => a.daysUntil - b.daysUntil));
  }, [vehicles]);

  const startRealTimeUpdates = useCallback(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        ...prev,
        timestamp: new Date(),
        activeVehicles: Math.floor(Math.random() * 10) + 45,
        totalTrips: Math.floor(Math.random() * 50) + 200,
        fuelConsumption: (Math.random() * 100 + 500).toFixed(1),
        averageUtilization: (Math.random() * 10 + 75).toFixed(1),
      }));
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const filterVehicles = useCallback(() => {
    let filtered = [...vehicles];

    // Filter by tab
    if (tabValue === 1) filtered = filtered.filter(v => v.status === 'active');
    if (tabValue === 2) filtered = filtered.filter(v => v.status === 'maintenance');
    if (tabValue === 3) filtered = filtered.filter(v => v.status === 'offline');
    if (tabValue === 4) filtered = filtered.filter(v => v.alerts.length > 0);
    if (tabValue === 5) filtered = filtered.filter(v => {
      const daysUntilMaintenance = Math.ceil((v.nextMaintenance - new Date()) / (1000 * 60 * 60 * 24));
      return daysUntilMaintenance <= 7;
    });

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(vehicle =>
        vehicle.id.toLowerCase().includes(term) ||
        vehicle.plateNumber.toLowerCase().includes(term) ||
        vehicle.make.toLowerCase().includes(term) ||
        vehicle.model.toLowerCase().includes(term) ||
        vehicle.driver?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(vehicle => vehicle.status === filterStatus);
    }

    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(vehicle => vehicle.type === filterType);
    }

    setFilteredVehicles(filtered);
  }, [vehicles, searchTerm, filterStatus, filterType, tabValue]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleVehicleAction = (action, vehicleId) => {
    switch (action) {
      case 'activate':
        setVehicles(prev => prev.map(v => 
          v.id === vehicleId ? { ...v, status: 'active' } : v
        ));
        showSnackbar('Vehicle activated successfully', 'success');
        break;
      case 'maintenance':
        setVehicles(prev => prev.map(v => 
          v.id === vehicleId ? { ...v, status: 'maintenance' } : v
        ));
        showSnackbar('Vehicle sent to maintenance', 'warning');
        break;
      case 'offline':
        setVehicles(prev => prev.map(v => 
          v.id === vehicleId ? { ...v, status: 'offline' } : v
        ));
        showSnackbar('Vehicle taken offline', 'info');
        break;
      default:
        break;
    }
  };

  const getFleetStats = () => {
    const total = vehicles.length;
    const active = vehicles.filter(v => v.status === 'active').length;
    const maintenance = vehicles.filter(v => v.status === 'maintenance').length;
    const offline = vehicles.filter(v => v.status === 'offline').length;
    const alerts = vehicles.reduce((sum, v) => sum + v.alerts.length, 0);
    const utilizationRate = vehicles.reduce((sum, v) => sum + parseFloat(v.performance.utilizationRate), 0) / total;

    return { total, active, maintenance, offline, alerts, utilizationRate: utilizationRate.toFixed(1) };
  };

  const stats = getFleetStats();

  const getStatusColor = (status) => {
    return statusConfig[status]?.color || 'default';
  };

  const getSensorStatusColor = (status) => {
    switch (status) {
      case 'normal': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Advanced Fleet Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<Map />}>
            Fleet Map
          </Button>
          <Button variant="outlined" startIcon={<Assessment />}>
            Analytics
          </Button>
          <Button variant="outlined" startIcon={<Download />}>
            Export
          </Button>
          <Button variant="contained" startIcon={<Add />}>
            Add Vehicle
          </Button>
        </Box>
      </Box>

      {/* Real-time Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Fleet', value: stats.total, icon: <DirectionsCar />, color: 'primary' },
          { title: 'Active Vehicles', value: stats.active, icon: <CheckCircle />, color: 'success' },
          { title: 'In Maintenance', value: stats.maintenance, icon: <Build />, color: 'warning' },
          { title: 'Offline', value: stats.offline, icon: <Error />, color: 'error' },
          { title: 'Active Alerts', value: stats.alerts, icon: <Warning />, color: 'warning' },
          { title: 'Avg Utilization', value: `${stats.utilizationRate}%`, icon: <Assessment />, color: 'info' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={2} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ color: `${stat.color}.main`, mb: 1 }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h5" fontWeight="bold">
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search vehicles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  {Object.entries(statusConfig).map(([key, config]) => (
                    <MenuItem key={key} value={key}>{config.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  label="Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  {Object.entries(vehicleTypes).map(([key, type]) => (
                    <MenuItem key={key} value={key}>{type.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button variant="outlined" startIcon={<CalendarToday />} fullWidth>
                  Maintenance Schedule
                </Button>
                <Button variant="outlined" startIcon={<Analytics />} fullWidth>
                  Performance Report
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Vehicle Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label={`All Vehicles (${stats.total})`} />
            <Tab label={`Active (${stats.active})`} />
            <Tab label={`Maintenance (${stats.maintenance})`} />
            <Tab label={`Offline (${stats.offline})`} />
            <Tab label={`Alerts (${stats.alerts})`} />
            <Tab label="Due Maintenance" />
          </Tabs>
        </Box>

        <CardContent>
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Vehicle</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Driver</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Fuel/Battery</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Health</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredVehicles.slice(0, 20).map((vehicle) => (
                  <TableRow key={vehicle.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                          {vehicleTypes[vehicle.type]?.icon || '🚗'}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {vehicle.id} - {vehicle.plateNumber}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {vehicle.make} {vehicle.model} ({vehicle.year})
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {vehicleTypes[vehicle.type]?.label} • {vehicle.mileage.toLocaleString()} km
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Chip
                          label={statusConfig[vehicle.status]?.label}
                          color={getStatusColor(vehicle.status)}
                          size="small"
                          icon={statusConfig[vehicle.status]?.icon}
                        />
                        {vehicle.alerts.length > 0 && (
                          <Badge badgeContent={vehicle.alerts.length} color="error" sx={{ ml: 1 }}>
                            <Warning color="warning" />
                          </Badge>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {vehicle.driver ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.75rem' }}>
                            {vehicle.driver.charAt(0)}
                          </Avatar>
                          <Typography variant="body2">{vehicle.driver}</Typography>
                        </Box>
                      ) : (
                        <Chip label="Unassigned" size="small" variant="outlined" />
                      )}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocationOn sx={{ mr: 0.5, fontSize: 16 }} />
                        <Box>
                          <Typography variant="body2">{vehicle.location.city}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {vehicle.location.address}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalGasStation sx={{ mr: 0.5, fontSize: 16 }} />
                          <Typography variant="body2">{vehicle.fuel}%</Typography>
                        </Box>
                        {vehicle.battery !== null && (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Battery80 sx={{ mr: 0.5, fontSize: 16 }} />
                            <Typography variant="body2">{vehicle.battery}%</Typography>
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {vehicle.performance.fuelEfficiency} km/l
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {vehicle.performance.utilizationRate}% utilization
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {Object.entries(vehicle.sensors).map(([sensor, status]) => (
                          <Tooltip key={sensor} title={`${sensor}: ${status}`}>
                            <Chip
                              size="small"
                              label={sensor.charAt(0).toUpperCase()}
                              color={getSensorStatusColor(status)}
                              sx={{ minWidth: 24, fontSize: '0.6rem' }}
                            />
                          </Tooltip>
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          setMenuAnchor(e.currentTarget);
                          setSelectedVehicleForMenu(vehicle);
                        }}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Vehicle Actions Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          setSelectedVehicle(selectedVehicleForMenu);
          setDialogOpen(true);
          setMenuAnchor(null);
        }}>
          <Edit sx={{ mr: 1 }} /> Edit Vehicle
        </MenuItem>
        <MenuItem onClick={() => {
          setSelectedVehicle(selectedVehicleForMenu);
          setMaintenanceDialog(true);
          setMenuAnchor(null);
        }}>
          <Build sx={{ mr: 1 }} /> Schedule Maintenance
        </MenuItem>
        <MenuItem onClick={() => {
          setSelectedVehicle(selectedVehicleForMenu);
          setDocumentsDialog(true);
          setMenuAnchor(null);
        }}>
          <Description sx={{ mr: 1 }} /> View Documents
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          handleVehicleAction(
            selectedVehicleForMenu?.status === 'active' ? 'offline' : 'activate',
            selectedVehicleForMenu?.id
          );
          setMenuAnchor(null);
        }}>
          {selectedVehicleForMenu?.status === 'active' ? (
            <>
              <Error sx={{ mr: 1 }} /> Take Offline
            </>
          ) : (
            <>
              <CheckCircle sx={{ mr: 1 }} /> Activate
            </>
          )}
        </MenuItem>
      </Menu>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdvancedFleetManagement;
