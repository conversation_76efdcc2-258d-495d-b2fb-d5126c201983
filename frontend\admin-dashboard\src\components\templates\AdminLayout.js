import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Collapse,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  DirectionsCar as FleetIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  AccountCircle,
  Logout,
  Brightness4,
  Brightness7,
  ExpandLess,
  ExpandMore,
  MonetizationOn,
  LocalShipping,
  LiveTv,
  Security,
  Language,
  Help
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
    color: '#1976d2'
  },
  {
    text: 'Live Operations',
    icon: <LiveTv />,
    path: '/live-operations',
    color: '#d32f2f',
    badge: 'LIVE'
  },
  {
    text: 'User Management',
    icon: <PeopleIcon />,
    path: '/users',
    color: '#2e7d32',
    children: [
      { text: 'All Users', path: '/users/all' },
      { text: 'Drivers', path: '/users/drivers' },
      { text: 'Passengers', path: '/users/passengers' },
      { text: 'Admins', path: '/users/admins' }
    ]
  },
  {
    text: 'Fleet Management',
    icon: <FleetIcon />,
    path: '/fleet',
    color: '#ed6c02',
    children: [
      { text: 'Vehicles', path: '/fleet/vehicles' },
      { text: 'Maintenance', path: '/fleet/maintenance' },
      { text: 'Tracking', path: '/fleet/tracking' },
      { text: 'Reports', path: '/fleet/reports' }
    ]
  },
  {
    text: 'Financial Management',
    icon: <MonetizationOn />,
    path: '/finance',
    color: '#9c27b0',
    children: [
      { text: 'Payments', path: '/finance/payments' },
      { text: 'Invoices', path: '/finance/invoices' },
      { text: 'Revenue', path: '/finance/revenue' },
      { text: 'Expenses', path: '/finance/expenses' }
    ]
  },
  {
    text: 'Parcel Management',
    icon: <LocalShipping />,
    path: '/parcels',
    color: '#00796b',
    children: [
      { text: 'Active Deliveries', path: '/parcels/active' },
      { text: 'Completed', path: '/parcels/completed' },
      { text: 'Tracking', path: '/parcels/tracking' }
    ]
  },
  {
    text: 'Analytics',
    icon: <AnalyticsIcon />,
    path: '/analytics',
    color: '#5e35b1',
    children: [
      { text: 'Performance', path: '/analytics/performance' },
      { text: 'Revenue Analytics', path: '/analytics/revenue' },
      { text: 'User Behavior', path: '/analytics/behavior' },
      { text: 'Predictions', path: '/analytics/predictions' }
    ]
  },
  {
    text: 'System Settings',
    icon: <SettingsIcon />,
    path: '/settings',
    color: '#424242',
    children: [
      { text: 'General', path: '/settings/general' },
      { text: 'Security', path: '/settings/security' },
      { text: 'Integrations', path: '/settings/integrations' },
      { text: 'Backup', path: '/settings/backup' }
    ]
  }
];

const AdminLayout = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationAnchor, setNotificationAnchor] = useState(null);
  const [expandedItems, setExpandedItems] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationOpen = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  const handleItemClick = (item) => {
    if (item.children) {
      setExpandedItems(prev => ({
        ...prev,
        [item.text]: !prev[item.text]
      }));
    } else {
      setIsLoading(true);
      navigate(item.path);
      if (isMobile) {
        setMobileOpen(false);
      }
      setTimeout(() => setIsLoading(false), 500);
    }
  };

  const isActive = (path) => {
    return location.pathname.startsWith(path);
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          TECNO DRIVE
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Advanced Fleet Management
        </Typography>
      </Box>
      
      <List sx={{ flexGrow: 1, px: 1 }}>
        {menuItems.map((item) => (
          <React.Fragment key={item.text}>
            <ListItem disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleItemClick(item)}
                sx={{
                  borderRadius: 2,
                  backgroundColor: isActive(item.path) ? `${item.color}15` : 'transparent',
                  border: isActive(item.path) ? `1px solid ${item.color}30` : '1px solid transparent',
                  '&:hover': {
                    backgroundColor: `${item.color}10`,
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                <ListItemIcon sx={{ color: isActive(item.path) ? item.color : 'text.secondary' }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text}
                  sx={{ 
                    '& .MuiListItemText-primary': {
                      fontWeight: isActive(item.path) ? 600 : 400,
                      color: isActive(item.path) ? item.color : 'text.primary'
                    }
                  }}
                />
                {item.badge && (
                  <Chip 
                    label={item.badge} 
                    size="small" 
                    color="error" 
                    sx={{ fontSize: '0.7rem', height: 20 }}
                  />
                )}
                {item.children && (
                  expandedItems[item.text] ? <ExpandLess /> : <ExpandMore />
                )}
              </ListItemButton>
            </ListItem>
            
            {item.children && (
              <Collapse in={expandedItems[item.text]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding sx={{ pl: 2 }}>
                  {item.children.map((child) => (
                    <ListItem key={child.text} disablePadding sx={{ mb: 0.5 }}>
                      <ListItemButton
                        onClick={() => {
                          setIsLoading(true);
                          navigate(child.path);
                          if (isMobile) setMobileOpen(false);
                          setTimeout(() => setIsLoading(false), 500);
                        }}
                        sx={{
                          borderRadius: 2,
                          backgroundColor: location.pathname === child.path ? `${item.color}10` : 'transparent',
                          '&:hover': {
                            backgroundColor: `${item.color}08`,
                          },
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <ListItemText 
                          primary={child.text}
                          sx={{ 
                            '& .MuiListItemText-primary': {
                              fontSize: '0.9rem',
                              fontWeight: location.pathname === child.path ? 600 : 400,
                              color: location.pathname === child.path ? item.color : 'text.secondary'
                            }
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>
      
      <Divider />
      <Box sx={{ p: 2 }}>
        <Typography variant="caption" color="text.secondary">
          Version 2.0.1 • © 2024 TECNO DRIVE
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          backgroundColor: 'background.paper',
          color: 'text.primary',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => isActive(item.path))?.text || 'Dashboard'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Notifications">
              <IconButton color="inherit" onClick={handleNotificationOpen}>
                <Badge badgeContent={4} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Profile">
              <IconButton onClick={handleProfileMenuOpen} sx={{ p: 0 }}>
                <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                  A
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
        {isLoading && <LinearProgress />}
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          backgroundColor: 'background.default',
          minHeight: 'calc(100vh - 64px)'
        }}
      >
        {children}
      </Box>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon><AccountCircle /></ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon><Security /></ListItemIcon>
          Security
        </MenuItem>
        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon><Language /></ListItemIcon>
          Language
        </MenuItem>
        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon><Help /></ListItemIcon>
          Help
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon><Logout /></ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{ sx: { width: 320, maxHeight: 400 } }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">Notifications</Typography>
        </Box>
        <MenuItem>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              New ride request
            </Typography>
            <Typography variant="caption" color="text.secondary">
              2 minutes ago
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Vehicle maintenance due
            </Typography>
            <Typography variant="caption" color="text.secondary">
              1 hour ago
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Payment processed
            </Typography>
            <Typography variant="caption" color="text.secondary">
              3 hours ago
            </Typography>
          </Box>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default AdminLayout;
