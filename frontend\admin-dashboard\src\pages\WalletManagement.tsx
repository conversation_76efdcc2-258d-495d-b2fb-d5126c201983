import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  LinearProgress,
  Tooltip,
  Fab,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  AccountBalanceWallet,
  Add,
  Remove,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  CreditCard,
  AccountBalance,
  Payment,
  Receipt,
  History,
  Refresh,
  Download,
  Upload,
  Security,
  Verified,
  Warning,
  Error,
  CheckCircle,
  Info,
  Edit,
  Delete,
  Visibility,
  MoreVert,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/material/styles';

interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit' | 'transfer' | 'refund';
  amount: number;
  currency: string;
  description: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  timestamp: Date;
  reference: string;
  userId?: string;
  userName?: string;
  category: string;
}

interface WalletBalance {
  userId: string;
  userName: string;
  email: string;
  balance: number;
  currency: string;
  status: 'active' | 'suspended' | 'frozen';
  lastActivity: Date;
  totalCredits: number;
  totalDebits: number;
  avatar?: string;
}

const WalletManagement: React.FC = () => {
  const theme = useTheme();
  const [wallets, setWallets] = useState<WalletBalance[]>([]);
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<WalletBalance | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [transactionDialog, setTransactionDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' | 'warning' | 'info' });

  const [newTransaction, setNewTransaction] = useState({
    type: 'credit' as 'credit' | 'debit',
    amount: '',
    description: '',
    category: 'manual',
  });

  useEffect(() => {
    loadWallets();
    loadTransactions();
  }, []);

  const loadWallets = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockWallets: WalletBalance[] = [
        {
          userId: 'user-001',
          userName: 'أحمد محمد',
          email: '<EMAIL>',
          balance: 1250.75,
          currency: 'SAR',
          status: 'active',
          lastActivity: new Date(Date.now() - 3600000),
          totalCredits: 5000,
          totalDebits: 3749.25,
        },
        {
          userId: 'user-002',
          userName: 'فاطمة علي',
          email: '<EMAIL>',
          balance: 890.50,
          currency: 'SAR',
          status: 'active',
          lastActivity: new Date(Date.now() - 7200000),
          totalCredits: 2500,
          totalDebits: 1609.50,
        },
        {
          userId: 'user-003',
          userName: 'محمد السعيد',
          email: '<EMAIL>',
          balance: 0,
          currency: 'SAR',
          status: 'suspended',
          lastActivity: new Date(Date.now() - 86400000),
          totalCredits: 1000,
          totalDebits: 1000,
        },
      ];
      setWallets(mockWallets);
    } catch (error) {
      console.error('Error loading wallets:', error);
      setSnackbar({ open: true, message: 'خطأ في تحميل المحافظ', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    try {
      // Mock data - replace with actual API call
      const mockTransactions: WalletTransaction[] = [
        {
          id: 'txn-001',
          type: 'credit',
          amount: 500,
          currency: 'SAR',
          description: 'إيداع رصيد',
          status: 'completed',
          timestamp: new Date(Date.now() - 1800000),
          reference: 'REF-001',
          userId: 'user-001',
          userName: 'أحمد محمد',
          category: 'deposit',
        },
        {
          id: 'txn-002',
          type: 'debit',
          amount: 25.50,
          currency: 'SAR',
          description: 'دفع رحلة',
          status: 'completed',
          timestamp: new Date(Date.now() - 3600000),
          reference: 'REF-002',
          userId: 'user-002',
          userName: 'فاطمة علي',
          category: 'ride_payment',
        },
        {
          id: 'txn-003',
          type: 'transfer',
          amount: 100,
          currency: 'SAR',
          description: 'تحويل بين المحافظ',
          status: 'pending',
          timestamp: new Date(Date.now() - 900000),
          reference: 'REF-003',
          category: 'transfer',
        },
      ];
      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error loading transactions:', error);
    }
  };

  const handleAddTransaction = async () => {
    if (!selectedWallet || !newTransaction.amount || !newTransaction.description) {
      setSnackbar({ open: true, message: 'يرجى ملء جميع الحقول المطلوبة', severity: 'warning' });
      return;
    }

    try {
      const amount = parseFloat(newTransaction.amount);
      const transaction: WalletTransaction = {
        id: `txn-${Date.now()}`,
        type: newTransaction.type,
        amount,
        currency: 'SAR',
        description: newTransaction.description,
        status: 'completed',
        timestamp: new Date(),
        reference: `REF-${Date.now()}`,
        userId: selectedWallet.userId,
        userName: selectedWallet.userName,
        category: newTransaction.category,
      };

      // Update wallet balance
      const updatedWallets = wallets.map(wallet => {
        if (wallet.userId === selectedWallet.userId) {
          const newBalance = newTransaction.type === 'credit' 
            ? wallet.balance + amount 
            : wallet.balance - amount;
          
          return {
            ...wallet,
            balance: newBalance,
            totalCredits: newTransaction.type === 'credit' ? wallet.totalCredits + amount : wallet.totalCredits,
            totalDebits: newTransaction.type === 'debit' ? wallet.totalDebits + amount : wallet.totalDebits,
            lastActivity: new Date(),
          };
        }
        return wallet;
      });

      setWallets(updatedWallets);
      setTransactions([transaction, ...transactions]);
      setTransactionDialog(false);
      setNewTransaction({ type: 'credit', amount: '', description: '', category: 'manual' });
      setSnackbar({ open: true, message: 'تم إضافة المعاملة بنجاح', severity: 'success' });
    } catch (error) {
      console.error('Error adding transaction:', error);
      setSnackbar({ open: true, message: 'خطأ في إضافة المعاملة', severity: 'error' });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'suspended': return 'warning';
      case 'frozen': return 'error';
      default: return 'default';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'credit': return <TrendingUp color="success" />;
      case 'debit': return <TrendingDown color="error" />;
      case 'transfer': return <Payment color="primary" />;
      case 'refund': return <Receipt color="info" />;
      default: return <AttachMoney />;
    }
  };

  const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
  const activeWallets = wallets.filter(w => w.status === 'active').length;
  const todayTransactions = transactions.filter(t => 
    t.timestamp.toDateString() === new Date().toDateString()
  ).length;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          إدارة المحافظ الإلكترونية
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<Download />}>
            تصدير
          </Button>
          <Button variant="outlined" startIcon={<Refresh />} onClick={loadWallets}>
            تحديث
          </Button>
          <Button 
            variant="contained" 
            startIcon={<Add />}
            onClick={() => setTransactionDialog(true)}
          >
            إضافة معاملة
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    إجمالي الرصيد
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {totalBalance.toLocaleString()} ر.س
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                  <AccountBalanceWallet />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    المحافظ النشطة
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {activeWallets}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.success.main }}>
                  <CheckCircle />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    معاملات اليوم
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {todayTransactions}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                  <History />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    متوسط الرصيد
                  </Typography>
                  <Typography variant="h5" fontWeight="bold">
                    {wallets.length > 0 ? (totalBalance / wallets.length).toFixed(2) : 0} ر.س
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Wallets Table */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            المحافظ الإلكترونية
          </Typography>
          {loading && <LinearProgress sx={{ mb: 2 }} />}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>المستخدم</TableCell>
                  <TableCell>الرصيد</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>آخر نشاط</TableCell>
                  <TableCell>إجمالي الإيداعات</TableCell>
                  <TableCell>إجمالي السحوبات</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <AnimatePresence>
                  {wallets.map((wallet, index) => (
                    <motion.tr
                      key={wallet.userId}
                      component={TableRow}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar src={wallet.avatar}>
                            {wallet.userName.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {wallet.userName}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {wallet.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="h6" fontWeight="bold" color={wallet.balance >= 0 ? 'success.main' : 'error.main'}>
                          {wallet.balance.toLocaleString()} {wallet.currency}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={wallet.status === 'active' ? 'نشط' : wallet.status === 'suspended' ? 'معلق' : 'مجمد'}
                          color={getStatusColor(wallet.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {wallet.lastActivity.toLocaleString('ar-SA')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="success.main">
                          +{wallet.totalCredits.toLocaleString()} {wallet.currency}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="error.main">
                          -{wallet.totalDebits.toLocaleString()} {wallet.currency}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="عرض التفاصيل">
                            <IconButton 
                              size="small"
                              onClick={() => {
                                setSelectedWallet(wallet);
                                setDialogOpen(true);
                              }}
                            >
                              <Visibility />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="إضافة معاملة">
                            <IconButton 
                              size="small"
                              onClick={() => {
                                setSelectedWallet(wallet);
                                setTransactionDialog(true);
                              }}
                            >
                              <Add />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Transaction Dialog */}
      <Dialog open={transactionDialog} onClose={() => setTransactionDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>إضافة معاملة جديدة</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            {selectedWallet && (
              <Alert severity="info">
                المحفظة المحددة: {selectedWallet.userName} - الرصيد الحالي: {selectedWallet.balance.toLocaleString()} ر.س
              </Alert>
            )}
            
            <FormControl fullWidth>
              <InputLabel>نوع المعاملة</InputLabel>
              <Select
                value={newTransaction.type}
                label="نوع المعاملة"
                onChange={(e) => setNewTransaction({...newTransaction, type: e.target.value as 'credit' | 'debit'})}
              >
                <MenuItem value="credit">إيداع</MenuItem>
                <MenuItem value="debit">سحب</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              fullWidth
              label="المبلغ (ر.س)"
              type="number"
              value={newTransaction.amount}
              onChange={(e) => setNewTransaction({...newTransaction, amount: e.target.value})}
            />
            
            <TextField
              fullWidth
              label="الوصف"
              multiline
              rows={3}
              value={newTransaction.description}
              onChange={(e) => setNewTransaction({...newTransaction, description: e.target.value})}
            />
            
            <FormControl fullWidth>
              <InputLabel>الفئة</InputLabel>
              <Select
                value={newTransaction.category}
                label="الفئة"
                onChange={(e) => setNewTransaction({...newTransaction, category: e.target.value})}
              >
                <MenuItem value="manual">يدوي</MenuItem>
                <MenuItem value="deposit">إيداع</MenuItem>
                <MenuItem value="ride_payment">دفع رحلة</MenuItem>
                <MenuItem value="refund">استرداد</MenuItem>
                <MenuItem value="bonus">مكافأة</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTransactionDialog(false)}>
            إلغاء
          </Button>
          <Button variant="contained" onClick={handleAddTransaction}>
            إضافة المعاملة
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({...snackbar, open: false})}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({...snackbar, open: false})}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default WalletManagement;
