@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE FINAL HEALTH CHECK
echo ========================================
echo.

set healthy=0
set total=9

echo Checking all running services...
echo.

:: Check Eureka Server
echo [1] Eureka Server (8761):
curl -s -w "%%{http_code}" http://localhost:8761/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Service Discovery is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Config Server
echo [2] Config Server (8888):
curl -s -w "%%{http_code}" http://localhost:8888/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Configuration service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check API Gateway
echo [3] API Gateway (8080):
curl -s -w "%%{http_code}" http://localhost:8080/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - API Gateway is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Auth Service
echo [4] Auth Service (8081):
curl -s -w "%%{http_code}" http://localhost:8081/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Authentication service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check User Service
echo [5] User Service (8082):
curl -s -w "%%{http_code}" http://localhost:8082/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - User management service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Payment Service
echo [6] Payment Service (8083):
curl -s -w "%%{http_code}" http://localhost:8083/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Payment service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Ride Service
echo [7] Ride Service (8084):
curl -s -w "%%{http_code}" http://localhost:8084/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Ride management service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Fleet Service
echo [8] Fleet Service (8085):
curl -s -w "%%{http_code}" http://localhost:8085/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Fleet management service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Location Service
echo [9] Location Service (8086):
curl -s -w "%%{http_code}" http://localhost:8086/actuator/health -o nul > temp.txt
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Location service is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

echo.
echo ========================================
echo    HEALTH CHECK SUMMARY
echo ========================================
echo.
echo Healthy Services: %healthy%/%total%

if %healthy% geq 7 (
    echo.
    echo 🎉 PLATFORM IS OPERATIONAL!
    echo Most critical services are running.
    echo.
    echo 🌐 Access Points:
    echo - Eureka Dashboard: http://localhost:8761
    echo - API Gateway: http://localhost:8080
    echo - Service Health: http://localhost:8080/actuator/health
    echo.
    echo 📊 Platform Status: READY FOR USE
) else (
    echo.
    echo ⚠️  PLATFORM NEEDS ATTENTION
    set /a unhealthy=%total%-%healthy%
    echo %unhealthy% services are not responding properly.
    echo Check the logs\ directory for error details.
)

echo.
pause
