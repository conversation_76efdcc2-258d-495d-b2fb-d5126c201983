@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE Platform Startup
echo ========================================
echo.

:: Check Java and Maven
echo [SYSTEM CHECK] Verifying prerequisites...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found. Please install Java 17+
    pause
    exit /b 1
)

mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Maven not found. Please install Maven
    pause
    exit /b 1
)

echo ✅ Java and Maven are available
echo.

:: Build all services
echo [BUILD] Building all microservices...
mvn clean install -DskipTests -q
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo ✅ All services built successfully
echo.

:: Start Infrastructure Services
echo ========================================
echo    STARTING INFRASTRUCTURE SERVICES
echo ========================================

echo [1/4] Starting Eureka Server (Service Discovery)...
start "Eureka Server" cmd /k "cd microservices\infrastructure\eureka-server && mvn spring-boot:run"
timeout /t 30 /nobreak >nul

echo [2/4] Starting Config Server...
start "Config Server" cmd /k "cd microservices\infrastructure\config-server && mvn spring-boot:run"
timeout /t 20 /nobreak >nul

echo [3/4] Starting API Gateway...
start "API Gateway" cmd /k "cd microservices\infrastructure\api-gateway && mvn spring-boot:run"
timeout /t 20 /nobreak >nul

echo [4/4] Starting Monitoring Service...
start "Monitoring Service" cmd /k "cd microservices\infrastructure\monitoring-service && mvn spring-boot:run"
timeout /t 15 /nobreak >nul

echo ✅ Infrastructure services started
echo.

:: Start Core Services
echo ========================================
echo    STARTING CORE SERVICES
echo ========================================

echo [1/3] Starting Auth Service...
start "Auth Service" cmd /k "cd microservices\core\auth-service && mvn spring-boot:run"
timeout /t 15 /nobreak >nul

echo [2/3] Starting User Service...
start "User Service" cmd /k "cd microservices\core\user-service && mvn spring-boot:run"
timeout /t 15 /nobreak >nul

echo [3/3] Starting Payment Service...
start "Payment Service" cmd /k "cd microservices\core\payment-service && mvn spring-boot:run"
timeout /t 15 /nobreak >nul

echo ✅ Core services started
echo.

echo ========================================
echo    TECNO DRIVE PLATFORM STARTED!
echo ========================================
echo.
echo Infrastructure Services:
echo - Eureka Server: http://localhost:8761
echo - Config Server: http://localhost:8888
echo - API Gateway: http://localhost:8080
echo - Monitoring: http://localhost:9090
echo.
echo Core Services:
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8082
echo - Payment Service: http://localhost:8083
echo.
echo Next: Run start-business-services.bat to start business services
echo.
pause
