package com.tecnodrive.gateway.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * TECNO DRIVE - Logging Filter
 * 
 * Logs all requests and responses passing through the API Gateway
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@Component
public class LoggingFilter implements GlobalFilter, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(LoggingFilter.class);
    private static final String REQUEST_ID_HEADER = "X-Request-ID";
    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final String UNKNOWN = "unknown";
    private static final String ANONYMOUS = "anonymous";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // Generate unique request ID
        String requestId = UUID.randomUUID().toString();
        
        // Add request ID to headers for downstream services
        ServerHttpRequest modifiedRequest = request.mutate()
            .header(REQUEST_ID_HEADER, requestId)
            .build();
        
        // Record start time
        exchange.getAttributes().put(START_TIME_ATTRIBUTE, System.currentTimeMillis());
        
        // Log incoming request
        logRequest(requestId, request);
        
        return chain.filter(exchange.mutate().request(modifiedRequest).build())
            .doOnSuccess(aVoid -> logResponse(requestId, exchange))
            .doOnError(throwable -> logError(requestId, exchange, throwable));
    }

    private void logRequest(String requestId, ServerHttpRequest request) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String clientIp = getClientIp(request);
        String userAgent = request.getHeaders().getFirst("User-Agent");
        String userId = request.getHeaders().getFirst("X-User-Id");
        
        logger.info("🔵 INCOMING REQUEST [{}] {} {} {} from {} | User: {} | UA: {}",
            requestId,
            timestamp,
            request.getMethod(),
            request.getURI(),
            clientIp,
            userId != null ? userId : ANONYMOUS,
            userAgent != null ? userAgent : UNKNOWN
        );
        
        // Log headers (excluding sensitive ones)
        request.getHeaders().forEach((name, values) -> {
            if (!isSensitiveHeader(name)) {
                logger.debug("📋 REQUEST HEADER [{}] {}: {}", requestId, name, values);
            }
        });
    }

    private void logResponse(String requestId, ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        Long startTime = exchange.getAttribute(START_TIME_ATTRIBUTE);
        long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String statusCode = getStatusCodeSafely(response);

        logger.info("🟢 OUTGOING RESPONSE [{}] {} {} | Duration: {}ms",
            requestId,
            timestamp,
            statusCode,
            duration
        );
        
        // Log response headers (excluding sensitive ones)
        response.getHeaders().forEach((name, values) -> {
            if (!isSensitiveHeader(name)) {
                logger.debug("📋 RESPONSE HEADER [{}] {}: {}", requestId, name, values);
            }
        });
    }

    private void logError(String requestId, ServerWebExchange exchange, Throwable throwable) {
        Long startTime = exchange.getAttribute(START_TIME_ATTRIBUTE);
        long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        logger.error("🔴 ERROR RESPONSE [{}] {} | Duration: {}ms | Error: {}", 
            requestId,
            timestamp,
            duration,
            throwable.getMessage(),
            throwable
        );
    }

    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        try {
            var remoteAddress = request.getRemoteAddress();
            if (remoteAddress != null) {
                var address = remoteAddress.getAddress();
                if (address != null) {
                    return address.getHostAddress();
                }
            }
        } catch (Exception e) {
            // Log error if needed
        }
        return UNKNOWN;
    }

    private String getStatusCodeSafely(ServerHttpResponse response) {
        try {
            var statusCode = response.getStatusCode();
            return statusCode != null ? statusCode.toString() : UNKNOWN;
        } catch (Exception e) {
            return UNKNOWN;
        }
    }

    private boolean isSensitiveHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.contains("authorization") ||
               lowerCaseName.contains("password") ||
               lowerCaseName.contains("token") ||
               lowerCaseName.contains("secret") ||
               lowerCaseName.contains("key");
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
