@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TECNO DRIVE PLATFORM STARTUP
echo ========================================
echo.

:: Create logs directory
if not exist "logs" mkdir logs

echo [STEP 1] Starting Eureka Server...
start "Eureka Server" cmd /k "cd microservices\infrastructure\eureka-server && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8761' > ..\..\..\..\logs\eureka.log 2>&1"

echo Waiting 45 seconds for Eureka to start...
timeout /t 45 /nobreak >nul

:: Check if Eureka is running
curl -s http://localhost:8761/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Eureka Server is running
) else (
    echo ❌ Eureka Server failed to start
    echo Check logs\eureka.log for details
    pause
    exit /b 1
)

echo.
echo [STEP 2] Starting Config Server...
start "Config Server" cmd /k "cd microservices\infrastructure\config-server && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8888' > ..\..\..\..\logs\config.log 2>&1"

echo Waiting 30 seconds for Config Server...
timeout /t 30 /nobreak >nul

echo.
echo [STEP 3] Starting API Gateway...
start "API Gateway" cmd /k "cd microservices\infrastructure\api-gateway && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8080' > ..\..\..\..\logs\gateway.log 2>&1"

echo Waiting 30 seconds for API Gateway...
timeout /t 30 /nobreak >nul

echo.
echo [STEP 4] Starting Core Services...

start "Auth Service" cmd /k "cd microservices\core\auth-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8081' > ..\..\..\logs\auth.log 2>&1"
timeout /t 15 /nobreak >nul

start "User Service" cmd /k "cd microservices\core\user-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8082' > ..\..\..\logs\user.log 2>&1"
timeout /t 15 /nobreak >nul

start "Payment Service" cmd /k "cd microservices\core\payment-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8083' > ..\..\..\logs\payment.log 2>&1"
timeout /t 15 /nobreak >nul

echo.
echo [STEP 5] Starting Business Services...

start "Ride Service" cmd /k "cd microservices\business\ride-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8084' > ..\..\..\..\logs\ride.log 2>&1"
timeout /t 10 /nobreak >nul

start "Fleet Service" cmd /k "cd microservices\business\fleet-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8085' > ..\..\..\..\logs\fleet.log 2>&1"
timeout /t 10 /nobreak >nul

start "Location Service" cmd /k "cd microservices\business\location-service && mvn spring-boot:run -Dspring-boot.run.jvmArguments='-Dserver.port=8086' > ..\..\..\..\logs\location.log 2>&1"
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo    PLATFORM STARTUP COMPLETE
echo ========================================
echo.
echo Services Started:
echo - Eureka Server: http://localhost:8761
echo - Config Server: http://localhost:8888  
echo - API Gateway: http://localhost:8080
echo - Auth Service: http://localhost:8081
echo - User Service: http://localhost:8082
echo - Payment Service: http://localhost:8083
echo - Ride Service: http://localhost:8084
echo - Fleet Service: http://localhost:8085
echo - Location Service: http://localhost:8086
echo.
echo Check logs\ directory for service logs
echo Run health-check-final.bat to verify all services
echo.
pause
