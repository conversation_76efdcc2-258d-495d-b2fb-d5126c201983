@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    FRONTEND APPLICATIONS HEALTH CHECK
echo ========================================
echo.

set healthy=0
set total=5

echo Checking all frontend applications...
echo.

:: Check Admin Dashboard
echo [1] Admin Dashboard (Port 3000):
curl -s -w "%%{http_code}" http://localhost:3000 -o nul > temp.txt 2>nul
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Admin Dashboard is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Live Operations Dashboard
echo [2] Live Operations Dashboard (Port 3001):
curl -s -w "%%{http_code}" http://localhost:3001 -o nul > temp.txt 2>nul
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Live Operations Dashboard is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Operator Dashboard
echo [3] Operator Dashboard (Port 4200):
curl -s -w "%%{http_code}" http://localhost:4200 -o nul > temp.txt 2>nul
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Operator Dashboard is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Driver App
echo [4] Driver App (Port 19006):
curl -s -w "%%{http_code}" http://localhost:19006 -o nul > temp.txt 2>nul
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Driver App is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

:: Check Passenger App
echo [5] Passenger App (Port 19007):
curl -s -w "%%{http_code}" http://localhost:19007 -o nul > temp.txt 2>nul
set /p status=<temp.txt
if "!status!"=="200" (
    echo ✅ HEALTHY - Passenger App is running
    set /a healthy+=1
) else (
    echo ❌ UNHEALTHY - Status: !status!
)
del temp.txt 2>nul

echo.
echo ========================================
echo    FRONTEND HEALTH SUMMARY
echo ========================================
echo.
echo Healthy Applications: %healthy%/%total%

if %healthy% geq 3 (
    echo.
    echo 🎉 FRONTEND PLATFORM IS OPERATIONAL!
    echo.
    echo 🌐 Access Points:
    echo - Admin Dashboard: http://localhost:3000
    echo - Live Operations: http://localhost:3001
    echo - Operator Dashboard: http://localhost:4200
    echo - Driver App: http://localhost:19006
    echo - Passenger App: http://localhost:19007
    echo.
    echo 📊 Frontend Status: READY FOR USE
) else (
    echo.
    echo ⚠️  FRONTEND PLATFORM NEEDS ATTENTION
    set /a unhealthy=%total%-%healthy%
    echo %unhealthy% applications are not responding properly.
    echo Check if all applications have been started correctly.
)

echo.
echo 🔗 Backend Integration:
echo - API Gateway: http://localhost:8080
echo - Eureka Dashboard: http://localhost:8761
echo - pgAdmin: http://localhost:5050
echo.
pause
