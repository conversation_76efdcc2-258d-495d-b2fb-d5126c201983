import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  <PERSON><PERSON>,
  Chip,
} from '@mui/material';
import {
  DirectionsCar,
  Build,
  LocationOn,
  Speed,
  LocalGasStation,
  Warning,
} from '@mui/icons-material';

const Fleet: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        Fleet Management
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DirectionsCar color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Vehicles</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                156
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active fleet size
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationOn color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Active Vehicles</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                142
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Currently on road
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Build color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Maintenance</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                8
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Vehicles in service
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Warning color="error" sx={{ mr: 1 }} />
                <Typography variant="h6">Issues</Typography>
              </Box>
              <Typography variant="h4" color="error.main">
                6
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Require attention
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Fleet Status Overview
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 3 }}>
                <Chip 
                  icon={<DirectionsCar />} 
                  label="142 Active" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<Build />} 
                  label="8 Maintenance" 
                  color="warning" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<Warning />} 
                  label="6 Issues" 
                  color="error" 
                  variant="outlined" 
                />
                <Chip 
                  icon={<LocalGasStation />} 
                  label="Fuel: 85%" 
                  color="info" 
                  variant="outlined" 
                />
              </Box>
              
              <Typography variant="h6" sx={{ mb: 2 }}>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button variant="contained" startIcon={<DirectionsCar />}>
                  Add Vehicle
                </Button>
                <Button variant="outlined" startIcon={<Build />}>
                  Schedule Maintenance
                </Button>
                <Button variant="outlined" startIcon={<LocationOn />}>
                  Track Fleet
                </Button>
                <Button variant="outlined" startIcon={<Speed />}>
                  Performance Report
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Fleet;
