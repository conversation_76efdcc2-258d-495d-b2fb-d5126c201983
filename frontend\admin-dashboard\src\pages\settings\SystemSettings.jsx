import React, { useState } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
} from '@mui/material';
import {
  Settings,
  Security,
  Notifications,
  Backup,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const SystemSettings = () => {
  const [settings, setSettings] = useState({
    notifications: {
      emailAlerts: true,
      smsAlerts: false,
      pushNotifications: true,
      maintenanceAlerts: true,
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
    },
    system: {
      autoBackup: true,
      backupFrequency: 'daily',
      maintenanceMode: false,
      debugMode: false,
    },
  });

  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value,
      },
    }));
  };

  const handleSave = () => {
    // Save settings logic here
    console.log('Settings saved:', settings);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
        System Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Notifications sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Notification Settings</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.notifications.emailAlerts}
                      onChange={(e) => handleSettingChange('notifications', 'emailAlerts', e.target.checked)}
                    />
                  }
                  label="Email Alerts"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.notifications.smsAlerts}
                      onChange={(e) => handleSettingChange('notifications', 'smsAlerts', e.target.checked)}
                    />
                  }
                  label="SMS Alerts"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.notifications.pushNotifications}
                      onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
                    />
                  }
                  label="Push Notifications"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.notifications.maintenanceAlerts}
                      onChange={(e) => handleSettingChange('notifications', 'maintenanceAlerts', e.target.checked)}
                    />
                  }
                  label="Maintenance Alerts"
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Security Settings */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Security sx={{ mr: 1, color: 'error.main' }} />
                  <Typography variant="h6">Security Settings</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.security.twoFactorAuth}
                      onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
                    />
                  }
                  label="Two-Factor Authentication"
                />
                
                <TextField
                  fullWidth
                  label="Session Timeout (minutes)"
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  margin="normal"
                />
                
                <TextField
                  fullWidth
                  label="Password Expiry (days)"
                  type="number"
                  value={settings.security.passwordExpiry}
                  onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
                  margin="normal"
                />
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* System Settings */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Settings sx={{ mr: 1, color: 'info.main' }} />
                  <Typography variant="h6">System Configuration</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.system.autoBackup}
                          onChange={(e) => handleSettingChange('system', 'autoBackup', e.target.checked)}
                        />
                      }
                      label="Automatic Backup"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.system.maintenanceMode}
                          onChange={(e) => handleSettingChange('system', 'maintenanceMode', e.target.checked)}
                        />
                      }
                      label="Maintenance Mode"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.system.debugMode}
                          onChange={(e) => handleSettingChange('system', 'debugMode', e.target.checked)}
                        />
                      }
                      label="Debug Mode"
                    />
                  </Grid>
                </Grid>

                {settings.system.maintenanceMode && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    Maintenance mode is enabled. The system will be unavailable to users.
                  </Alert>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button variant="outlined">
              Reset to Defaults
            </Button>
            <Button variant="contained" onClick={handleSave}>
              Save Settings
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemSettings;
