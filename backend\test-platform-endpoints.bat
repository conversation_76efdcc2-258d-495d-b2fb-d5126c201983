@echo off
echo ========================================
echo    TESTING PLATFORM ENDPOINTS
echo ========================================
echo.

echo [1] Testing Service Discovery:
curl -s http://localhost:8761

echo.
echo [2] Testing API Gateway Health:
curl -s http://localhost:8080/actuator/health

echo.
echo [3] Testing Auth Service:
curl -s http://localhost:8081/actuator/info

echo.
echo [4] Testing Fleet Service:
curl -s http://localhost:8084

echo.
echo [5] Testing Location Service:
curl -s http://localhost:8085

echo.
echo [6] Testing Gateway Routes:
echo Testing auth route through gateway:
curl -s http://localhost:8080/auth/actuator/health

echo.
echo Testing users route through gateway:
curl -s http://localhost:8080/users/actuator/health

echo.
echo ========================================
echo    ENDPOINT TESTING COMPLETE
echo ========================================
