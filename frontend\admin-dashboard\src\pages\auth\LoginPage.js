import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typo<PERSON>,
  Container,
  Avatar,
  InputAdornment,
  IconButton,
  Checkbox,
  FormControlLabel,
  Alert,
  LinearProgress,
  Divider,
  Chip,
  useTheme,
  alpha,
  Fade,
  Slide
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Lock,
  Email,
  Security,
  Dashboard,
  Speed,
  Analytics,
  CloudDone
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const LoginPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      icon: <Dashboard sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Advanced Dashboard',
      description: 'Real-time fleet monitoring and analytics'
    },
    {
      icon: <Speed sx={{ fontSize: 40, color: 'success.main' }} />,
      title: 'Live Operations',
      description: 'Monitor operations in real-time with instant updates'
    },
    {
      icon: <Analytics sx={{ fontSize: 40, color: 'warning.main' }} />,
      title: 'Smart Analytics',
      description: 'AI-powered insights and predictive analytics'
    },
    {
      icon: <CloudDone sx={{ fontSize: 40, color: 'info.main' }} />,
      title: 'Cloud Integration',
      description: 'Seamless cloud-based fleet management'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'rememberMe' ? checked : value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        // Store auth token
        localStorage.setItem('authToken', 'demo-token-123');
        localStorage.setItem('userRole', 'admin');
        localStorage.setItem('userName', 'Admin User');
        
        // Navigate to dashboard
        navigate('/dashboard');
      } else {
        setError('Invalid email or password. Use <EMAIL> / admin123');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'admin123',
      rememberMe: false
    });
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000" fill-opacity="0.1"%3E%3Ccircle cx="7" cy="7" r="7"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'
        }}
      />

      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 8,
            flexDirection: { xs: 'column', md: 'row' }
          }}
        >
          {/* Left Side - Features Showcase */}
          <Box
            sx={{
              flex: 1,
              display: { xs: 'none', md: 'block' },
              textAlign: 'center'
            }}
          >
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Typography
                variant="h2"
                sx={{
                  fontWeight: 'bold',
                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 2
                }}
              >
                TECNO DRIVE
              </Typography>
              <Typography variant="h5" color="text.secondary" sx={{ mb: 4 }}>
                Advanced Fleet Management Platform
              </Typography>
            </motion.div>

            <Fade in={true} timeout={1000}>
              <Card
                sx={{
                  p: 4,
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  borderRadius: 3,
                  minHeight: 200,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Slide
                  direction="up"
                  in={true}
                  timeout={500}
                  key={currentFeature}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    {features[currentFeature].icon}
                    <Typography variant="h6" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
                      {features[currentFeature].title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {features[currentFeature].description}
                    </Typography>
                  </Box>
                </Slide>
              </Card>
            </Fade>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Chip label="Real-time Tracking" color="primary" variant="outlined" />
              <Chip label="AI Analytics" color="secondary" variant="outlined" />
              <Chip label="Cloud-based" color="success" variant="outlined" />
              <Chip label="Mobile Ready" color="info" variant="outlined" />
            </Box>
          </Box>

          {/* Right Side - Login Form */}
          <Box sx={{ flex: 1, maxWidth: 400, width: '100%' }}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card
                sx={{
                  p: 4,
                  backgroundColor: alpha(theme.palette.background.paper, 0.95),
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  borderRadius: 3,
                  boxShadow: `0 20px 40px ${alpha(theme.palette.common.black, 0.1)}`
                }}
              >
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <Avatar
                    sx={{
                      width: 60,
                      height: 60,
                      bgcolor: 'primary.main',
                      mx: 'auto',
                      mb: 2
                    }}
                  >
                    <Security sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="h4" fontWeight="bold" gutterBottom>
                    Welcome Back
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Sign in to access your admin dashboard
                  </Typography>
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <form onSubmit={handleSubmit}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    sx={{ mb: 3 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    sx={{ mb: 3 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  <FormControlLabel
                    control={
                      <Checkbox
                        name="rememberMe"
                        checked={formData.rememberMe}
                        onChange={handleInputChange}
                        color="primary"
                      />
                    }
                    label="Remember me"
                    sx={{ mb: 3 }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      mb: 2,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      '&:hover': {
                        background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                      }
                    }}
                  >
                    {isLoading ? 'Signing In...' : 'Sign In'}
                  </Button>

                  {isLoading && <LinearProgress sx={{ mb: 2 }} />}

                  <Divider sx={{ my: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      OR
                    </Typography>
                  </Divider>

                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={handleDemoLogin}
                    sx={{ mb: 2 }}
                  >
                    Use Demo Credentials
                  </Button>

                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Demo: <EMAIL> / admin123
                    </Typography>
                  </Box>
                </form>
              </Card>
            </motion.div>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default LoginPage;
