import { Client } from '@stomp/stompjs';
import SockJ<PERSON> from 'sockjs-client';

class WebSocketService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.subscriptions = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    try {
      // Create SockJS connection
      const socket = new SockJS('http://localhost:8080/ws');

      this.client = new Client({
        webSocketFactory: () => socket,
        connectHeaders: {},
        debug: (str) => {
          console.log('STOMP Debug:', str);
        },
        reconnectDelay: this.reconnectDelay,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: (frame) => {
          console.log('WebSocket Connected:', frame);
          this.connected = true;
          this.reconnectAttempts = 0;
          this.onConnected();
        },
        onDisconnect: (frame) => {
          console.log('WebSocket Disconnected:', frame);
          this.connected = false;
          this.onDisconnected();
        },
        onStompError: (frame) => {
          console.error('STOMP Error:', frame);
          this.connected = false;
        },
        onWebSocketError: (error) => {
          console.error('WebSocket Error:', error);
          this.connected = false;
        },
        onWebSocketClose: (event) => {
          console.log('WebSocket Closed:', event);
          this.connected = false;
          this.handleReconnect();
        }
      });

      this.client.activate();
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      this.handleReconnect();
    }
  }

  disconnect() {
    if (this.client && this.client.connected) {
      // Unsubscribe from all topics
      this.subscriptions.forEach((subscription) => {
        if (subscription && typeof subscription.unsubscribe === 'function') {
          subscription.unsubscribe();
        }
      });
      this.subscriptions.clear();

      // Deactivate client
      this.client.deactivate();
    }
    this.connected = false;
  }

  subscribe(topic, callback) {
    if (!this.client || !this.connected) {
      console.warn('WebSocket not connected. Cannot subscribe to:', topic);
      return null;
    }

    try {
      const subscription = this.client.subscribe(topic, (message) => {
        try {
          const data = JSON.parse(message.body);
          callback(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          callback(message.body);
        }
      });

      this.subscriptions.set(topic, subscription);
      console.log('Subscribed to:', topic);

      return () => {
        if (subscription && typeof subscription.unsubscribe === 'function') {
          subscription.unsubscribe();
          this.subscriptions.delete(topic);
          console.log('Unsubscribed from:', topic);
        }
      };
    } catch (error) {
      console.error('Error subscribing to topic:', topic, error);
      return null;
    }
  }

  publish(destination, message) {
    if (!this.client || !this.connected) {
      console.warn('WebSocket not connected. Cannot publish to:', destination);
      return false;
    }

    try {
      this.client.publish({
        destination,
        body: JSON.stringify(message)
      });
      return true;
    } catch (error) {
      console.error('Error publishing message:', error);
      return false;
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached. Please refresh the page.');
    }
  }

  onConnected() {
    // Override in components if needed
    console.log('WebSocket connection established');
  }

  onDisconnected() {
    // Override in components if needed
    console.log('WebSocket connection lost');
  }

  isConnected() {
    return this.connected && this.client && this.client.connected;
  }

  getConnectionState() {
    if (!this.client) return 'DISCONNECTED';
    return this.client.state;
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export { webSocketService as WebSocketService };
export default webSocketService;