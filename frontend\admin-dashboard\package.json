{"name": "tecnodrive-admin-dashboard", "version": "1.0.0", "description": "TecnoDrive Advanced Admin Dashboard with Real-time Operations", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.115", "@mui/material": "^5.11.0", "@mui/x-data-grid": "^5.17.0", "@mui/x-date-pickers": "^5.0.0", "@stomp/stompjs": "^7.1.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "d3": "^7.8.0", "d3-axis": "^3.0.0", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "d3-shape": "^3.2.0", "date-fns": "^2.29.3", "formik": "^2.2.9", "framer-motion": "^12.23.12", "leaflet": "^1.9.3", "leaflet.heat": "^0.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "numeral": "^2.0.6", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-grid-layout": "^1.3.4", "react-helmet": "^6.1.0", "react-hot-toast": "^2.5.2", "react-leaflet": "^4.2.0", "react-query": "^3.39.0", "react-resizable": "^3.0.4", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "socket.io-client": "^4.6.0", "sockjs-client": "^1.6.1", "web-vitals": "^2.1.4", "yup": "^1.0.0", "zustand": "^4.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}